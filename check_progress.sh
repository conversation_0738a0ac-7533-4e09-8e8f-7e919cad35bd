#!/bin/bash

PROGRESS_FILE="/tmp/dsym_upload_progress.txt"

if [ ! -f "$PROGRESS_FILE" ]; then
  echo "Upload not in progress or progress file not found."
  exit 1
fi

PROGRESS=$(cat "$PROGRESS_FILE")
IFS='/' read -r CURRENT TOTAL <<< "$PROGRESS"

if [ -z "$CURRENT" ] || [ -z "$TOTAL" ]; then
  echo "Invalid progress data."
  exit 1
fi

PERCENT=$(echo "scale=2; ($CURRENT / $TOTAL) * 100" | bc)

# Get the process info
UPLOAD_PID=$(ps aux | grep upload_dsyms.sh | grep -v grep | awk '{print $2}')

if [ -z "$UPLOAD_PID" ]; then
  echo "Upload process is not running."
  echo "Last recorded progress: $CURRENT out of $TOTAL files ($PERCENT%)"
  exit 0
fi

# Get runtime
RUNTIME=$(ps -p "$UPLOAD_PID" -o etime= | tr -d ' ')

# Estimate remaining time
if [ "$CURRENT" -gt 0 ]; then
  # Calculate remaining files
  REMAINING_FILES=$((TOTAL - CURRENT))
  
  # On macOS, we can't use etimes, so we'll use a simple time per file estimate
  if [ "$REMAINING_FILES" -gt 0 ]; then
    # Assume ~30 seconds per file for remaining files
    REMAINING_MINUTES=$(echo "scale=0; ($REMAINING_FILES * 30) / 60" | bc)
    
    echo "Progress: $CURRENT out of $TOTAL files uploaded ($PERCENT%)"
    echo "Process has been running for $RUNTIME"
    echo "Estimated time remaining: ~$REMAINING_MINUTES minutes"
  else
    echo "Progress: $CURRENT out of $TOTAL files uploaded ($PERCENT%)"
    echo "Process has been running for $RUNTIME"
    echo "Almost complete!"
  fi
else
  echo "Progress: $CURRENT out of $TOTAL files uploaded ($PERCENT%)"
  echo "Process has been running for $RUNTIME"
  echo "Estimated time remaining: calculating..."
fi 