import UIKit
import Network

import Foundation
import Network

import AVFoundation
import AudioToolbox


protocol RTPReceiverDelegate: AnyObject {
    func handleFrameData(_ data: Data)
}

class Logger {
    static let shared = Logger()

    private let fileHandle: FileHandle?
    private let logQueue = DispatchQueue(label: "com.saigeware.logger")

    private init() {
        let fileManager = FileManager.default
        let logsDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let logFileURL = logsDirectory.appendingPathComponent("debug.log")

        // Create file if it doesn't exist
        if !fileManager.fileExists(atPath: logFileURL.path) {
            fileManager.createFile(atPath: logFileURL.path, contents: nil, attributes: nil)
        }

        fileHandle = try? FileHandle(forWritingTo: logFileURL)
        fileHandle?.seekToEndOfFile()
    }

    func log(_ message: String) {
        logQueue.async {
            let timestamp = ISO8601DateFormatter().string(from: Date())
            if let data = "[\(timestamp)] \(message)\n".data(using: .utf8) {
                self.fileHandle?.write(data)
            }
        }
    }

    deinit {
        fileHandle?.closeFile()
    }
}

class RTPReceiver {
    weak var delegate: RTPReceiverDelegate?
    let port: UInt16
    let bufferSize = 65536  // Large buffer to prevent packet loss
    let udpReceiveQueue = DispatchQueue(label: "com.udp.receive", qos: .userInteractive, attributes: .concurrent)
    
    init(port: UInt16) {
        self.port = port
    }
    
    func startReceiving() {
        udpReceiveQueue.async { [weak self] in
            guard let self = self else { return }
            self.receivePackets()
        }
    }
    
    private func receivePackets() {
        let socketFD = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP) // Create UDP socket
        guard socketFD >= 0 else {
            //print("Failed to create socket")
            Logger.shared.log("Failed to create socket")
            return
        }

        // Set socket options to avoid dropped packets
        var bufferSize = 65536
        setsockopt(socketFD, SOL_SOCKET, SO_RCVBUF, &bufferSize, socklen_t(MemoryLayout<Int>.size))
        
        // Bind socket to UDP port
        var addr = sockaddr_in()
        addr.sin_family = sa_family_t(AF_INET)
        addr.sin_port = port.bigEndian
        addr.sin_addr.s_addr = inet_addr("************")//INADDR_ANY

        let bindResult = withUnsafePointer(to: &addr) {
            $0.withMemoryRebound(to: sockaddr.self, capacity: 1) {
                bind(socketFD, $0, socklen_t(MemoryLayout<sockaddr_in>.size))
            }
        }

        guard bindResult >= 0 else {
            //print("Failed to bind socket")
            Logger.shared.log("Failed to bind socket")
            close(socketFD)
            return
        }

        //print("Listening on UDP port \(port)...")
        Logger.shared.log("Listening on UDP port \(port)...")

        // Start receiving packets in a continuous loop
        var buffer = [UInt8](repeating: 0, count: bufferSize)
        while true {
            let receivedBytes = recv(socketFD, &buffer, bufferSize, 0)
            if receivedBytes > 0 {
                let packetData = Data(buffer.prefix(receivedBytes))
                self.handlePacket(packetData)
            }
        }
    }

    private func handlePacket(_ data: Data) {
        DispatchQueue.main.async {
            self.delegate?.handleFrameData(data) // Notify delegate
        }
    }
}


class ViewController: UIViewController, RTPReceiverDelegate {

    let host = NWEndpoint.Host("************")
    let sendPort: NWEndpoint.Port = 20000
    let receivePort: NWEndpoint.Port = 10900
    var connection: NWConnection?
    var listener: NWListener?
    var receiver: RTPReceiver?
    let receiveQueue = DispatchQueue(label: "UDPReceiveQueue", qos: .userInteractive, attributes: .concurrent)

    let imageView = UIImageView()
    var currentFrameData = Data()
    var currentFrameIndex: Int = -1
    var expectedPacketIndex: Int = 0

    override func viewDidLoad() {
        super.viewDidLoad()
        
        setupUI()
        setupConnection()

        startUDPListener()
        receiver = RTPReceiver(port: receivePort.rawValue)  // Example RTP port
        receiver!.delegate = self
        receiver!.startReceiving()

        sendInitialCommands()
    }

    func setupUI() {
        imageView.frame = view.bounds
        imageView.contentMode = .scaleAspectFit
        view.addSubview(imageView)
        
        let shutterButton = UIButton(type: .system)
        shutterButton.setTitle("Capture", for: .normal)
        shutterButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .bold)
        shutterButton.backgroundColor = .red
        shutterButton.setTitleColor(.white, for: .normal)
        shutterButton.layer.cornerRadius = 25
        shutterButton.frame = CGRect(x: (view.frame.width - 100) / 2, y: view.frame.height - 80, width: 100, height: 50)
        shutterButton.addTarget(self, action: #selector(captureImage), for: .touchUpInside)
        view.addSubview(shutterButton)
    }

    @objc func captureImage() {
        guard let image = imageView.image else {
            //print("No image to save")
            Logger.shared.log("No image to save")
            return
        }
        playCameraShutterSound()
        UIImageWriteToSavedPhotosAlbum(image, nil, nil, nil)
        //print("Image saved to Photos")
        Logger.shared.log("Image saved to Photos")
    }
    
    func setupConnection() {
        connection = NWConnection(host: host, port: sendPort, using: .udp)
        connection?.start(queue: .main)
    }

    func startUDPListener() {
        receiveQueue.async {
            do {
                self.listener = try NWListener(using: .udp, on: self.sendPort)
                self.listener?.newConnectionHandler = { newConnection in
                    newConnection.start(queue: self.receiveQueue)
                    self.receiveData(on: newConnection)
                }
                self.listener?.start(queue: self.receiveQueue)
            } catch {
                print("Failed to start UDP listener: \(error)")
                //Logger.shared.log("Failed to start UDP listener: \(error)")
            }
        }
    }

    func sendInitialCommands() {
        sendCommand(Data([0x4A, 0x48, 0x43, 0x4D, 0x44, 0x10, 0x00]))
        sendCommand(Data([0x4A, 0x48, 0x43, 0x4D, 0x44, 0x20, 0x00]))
        sendHeartbeat()
    }

    func sendHeartbeat() {
        Logger.shared.log("Sending heartbeat...")
        sendCommand(Data([0x4A, 0x48, 0x43, 0x4D, 0x44, 0xD0, 0x01]))
    }

    func sendCommand(_ data: Data) {
        connection?.send(content: data, completion: .contentProcessed({ error in
            if let error = error {
                //print("Failed to send command: \(error)")
                Logger.shared.log("Failed to send command: \(error)")
            }
        }))
    }

    func receiveData(on connection: NWConnection) {
        connection.receive(minimumIncompleteLength: 1, maximumLength: 64) { data, context, isComplete, error in
            if let data = data, data.count > 4 {
                self.handleCommandData(data)
            }

            if error == nil {
                self.receiveData(on: connection)
            } else {
                Logger.shared.log("Command connection error: \(error!)")
            }
        }
    }
    
    func playCameraShutterSound() {
        AudioServicesPlaySystemSound(SystemSoundID(1108)) // 1108 = camera shutter
    }
    
    func handleCommandData(_ data: Data) {
        if let string = String(data: data, encoding: .utf8) {
            print("Converted string: \(string)")
            if(string.hasPrefix("JHCMD")) {
                if(Int(data[6]) == 1) {
                    DispatchQueue.main.async {
                        self.captureImage()
                    }
                }
            }
            
        } else {
            print("Failed to convert data to string.")
        }
    }
    
    func handleFrameData(_ data: Data) {
        let frameIndex = Int(data[0]) + Int(data[1]) * 256
        let packetIndex = Int(data[3])
        let bufType = Int(data[4])
        
        //print("FrameIndex \(frameIndex), PacketIndex \(packetIndex)")

        // Detect new frame
        if frameIndex != currentFrameIndex {
            currentFrameIndex = frameIndex
            expectedPacketIndex = 0
            
            if(!currentFrameData.isEmpty) {
                processCompleteFrame()
            }
        }

        // If packet is in sequence, append it
        if packetIndex == expectedPacketIndex {
            if(bufType == 49 || packetIndex > 0) {
                currentFrameData.append(data.subdata(in: 8..<data.count))
            }
            else {
                currentFrameData.append(data.subdata(in: 24..<data.count))
            }
            expectedPacketIndex += 1
        }

        // If packetIndex == 255 or packet size < 1450, assume frame complete
        /*if packetIndex == 255 || data.count < 1450 {
            processCompleteFrame()
        }*/

        // Send heartbeat every 50 frames
        if frameIndex % 50 == 0 {
            sendHeartbeat()
        }
    }

    func processCompleteFrame() {
        if let image = UIImage(data: currentFrameData) {
            DispatchQueue.main.async {
                self.imageView.image = image
            }
        }
        currentFrameData = Data()
    }

    deinit {
        Logger.shared.log("Deinit executed")
        sendCommand(Data([0x4A, 0x48, 0x43, 0x4D, 0x44, 0xD0, 0x02]))
    }
}


