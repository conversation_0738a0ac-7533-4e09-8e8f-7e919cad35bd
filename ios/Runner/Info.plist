<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>CADisableMinimumFrameDurationOnPhone</key>
    <true/>
    <key>CFBundleDevelopmentRegion</key>
    <string>$(DEVELOPMENT_LANGUAGE)</string>
    <key>CFBundleDisplayName</key>
    <string>GT+</string>
    <key>CFBundleExecutable</key>
    <string>$(EXECUTABLE_NAME)</string>
    <key>CFBundleIdentifier</key>
    <string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>CFBundleName</key>
    <string>gt_plus</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleShortVersionString</key>
    <string>$(FLUTTER_BUILD_NAME)</string>
    <key>CFBundleSignature</key>
    <string>????</string>
    <key>CFBundleVersion</key>
    <string>$(FLUTTER_BUILD_NUMBER)</string>
    <key>ITSAppUsesNonExemptEncryption</key>
    <false/>
    <key>LSRequiresIPhoneOS</key>
    <true/>
    <key>UIApplicationSupportsIndirectInputEvents</key>
    <true/>
    <key>UILaunchStoryboardName</key>
    <string>LaunchScreen</string>
    <key>UIMainStoryboardFile</key>
    <string>Main</string>
    <key>UIRequiresFullScreen</key>
    <true/>
    <key>NSCameraUsageDescription</key>
    <string>GT+ needs camera access to capture medical images for diagnostic analysis and patient monitoring</string>
    <key>NSBluetoothAlwaysUsageDescription</key>
    <string>GT+ uses Bluetooth to connect to medical devices such as blood pressure monitors, glucose meters, and pulse oximeters for capturing patient health data</string>
    <key>NSBluetoothPeripheralUsageDescription</key>
    <string>GT+ requires Bluetooth to connect with medical devices to collect vital patient health measurements</string>
    <key>NSLocalNetworkUsageDescription</key>
    <string>GT+ needs local network access to connect to and stream data from the microscope device for medical imaging</string>
    <key>NSPhotoLibraryAddUsageDescription</key>
    <string>GT+ needs permission to save captured medical images to your photo library for later analysis and patient record keeping</string>
    <key>NSPhotoLibraryUsageDescription</key>
    <string>GT+ requires access to your photo library to save and retrieve medical images for patient diagnostics</string>
    <key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
    <string>GT+ requests location access to verify WiFi connectivity purposes</string>
    <key>NSLocationAlwaysUsageDescription</key>
    <string>GT+ requests location access to verify WiFi connectivity purposes</string>
    <key>NSLocationWhenInUseUsageDescription</key>
    <string>GT+ requests location access to verify WiFi connectivity purposes</string>
    <key>UIBackgroundModes</key>
    <array>
      <string>bluetooth-central</string>
      <string>bluetooth-peripheral</string>
    </array>
    <key>UISupportedInterfaceOrientations~ipad</key>
    <array>
      <string>UIInterfaceOrientationLandscapeLeft</string>
      <string>UIInterfaceOrientationLandscapeRight</string>
    </array>
    <key>UISupportedInterfaceOrientations</key>
    <array>
      <string>UIInterfaceOrientationLandscapeLeft</string>
      <string>UIInterfaceOrientationLandscapeRight</string>
    </array>
  </dict>
</plist>