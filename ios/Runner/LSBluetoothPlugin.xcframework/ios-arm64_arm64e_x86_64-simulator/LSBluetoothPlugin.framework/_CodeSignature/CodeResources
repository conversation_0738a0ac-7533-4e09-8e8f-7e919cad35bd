<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/ATAlarmClockItem.h</key>
		<data>
		+8n/IPJU6XXJOK8LG7IybmKG5Hw=
		</data>
		<key>Headers/ATAutoRecognitionItem.h</key>
		<data>
		74c9QduCL+tt1sI+LsNaEYJKUQI=
		</data>
		<key>Headers/ATBatteryInfo.h</key>
		<data>
		olG3YKQkFueC2bwDdBjOiiwSa+s=
		</data>
		<key>Headers/ATBloodOxygenItem.h</key>
		<data>
		Go2nHECr00GmM3Q2e7IjQIS918s=
		</data>
		<key>Headers/ATBuriedPointItem.h</key>
		<data>
		AuvlvmXIUptlllEk3nqb0BWPCac=
		</data>
		<key>Headers/ATCavoProfiles.h</key>
		<data>
		0zbUU2SDibVKKNFouq5+O8fVip8=
		</data>
		<key>Headers/ATConfigItem.h</key>
		<data>
		qT2W/siw3GwGFvFxLGfVtG0DHXs=
		</data>
		<key>Headers/ATDataProfiles.h</key>
		<data>
		JKxya4GUI81xKPKgubx8ey0CVa4=
		</data>
		<key>Headers/ATDeviceData.h</key>
		<data>
		vRBuctYE5W6rZPl7hvXRugX91YU=
		</data>
		<key>Headers/ATDisturbItem.h</key>
		<data>
		tPk15xJJcC/XOL9LKMnBLQI9udQ=
		</data>
		<key>Headers/ATEventClockItem.h</key>
		<data>
		4XF5qVvqkdEi56NS529pS6xwk70=
		</data>
		<key>Headers/ATExerciseStatus.h</key>
		<data>
		gws0oypEIRbz9JlMhY933ve6LvQ=
		</data>
		<key>Headers/ATFileData.h</key>
		<data>
		AtXJ+xk9gM5J+T6/RDhauYj4DhY=
		</data>
		<key>Headers/ATFileUpdateMsg.h</key>
		<data>
		k1k002WEWGlB6RCkDbt87UxnH6E=
		</data>
		<key>Headers/ATGpsExerciseItem.h</key>
		<data>
		Yrkx6osk3Gtg2sT4r3NkJ5yStQk=
		</data>
		<key>Headers/ATHeartRateItem.h</key>
		<data>
		jl5iilx+ugSpdU/YyE1jlM7UUDo=
		</data>
		<key>Headers/ATHeartRateZoneItem.h</key>
		<data>
		64E6WRcAuUcCND8ViaXAm2Qnvhc=
		</data>
		<key>Headers/ATIotDevice.h</key>
		<data>
		JggoG4saLWc3cP2hyfdrnVbcDzI=
		</data>
		<key>Headers/ATIotInfo.h</key>
		<data>
		x5z7d1GeJuCC7hX3kCQuu3CDn44=
		</data>
		<key>Headers/ATLogItem.h</key>
		<data>
		zINgGIaroZ3QMh3fxH3DlvXsn2I=
		</data>
		<key>Headers/ATMeditationItem.h</key>
		<data>
		dNxedsw64Rn8OC96oDXOFalDL4c=
		</data>
		<key>Headers/ATMoodItem.h</key>
		<data>
		n9P4LFraOTtDj4PWgXtxSDoRBhs=
		</data>
		<key>Headers/ATPairSetting.h</key>
		<data>
		m7EMybzuBkfRloTlIemSVD7TQ2s=
		</data>
		<key>Headers/ATReminderItem.h</key>
		<data>
		sQ2zOdCCOyLnyuzv19SOL+lpvoo=
		</data>
		<key>Headers/ATSedentaryItem.h</key>
		<data>
		azA05mbb57VOeJ84Nd+2bcRwnKQ=
		</data>
		<key>Headers/ATSensorItem.h</key>
		<data>
		MwRik3RKKJfL0qgaKT1I5u0yom4=
		</data>
		<key>Headers/ATSettingProfiles.h</key>
		<data>
		fvYOC902YEXZPok12dNjSfqJxYM=
		</data>
		<key>Headers/ATSleepReportItem.h</key>
		<data>
		2nG695GB5Zs/dzAGOohd+w9f4fU=
		</data>
		<key>Headers/ATStepItem.h</key>
		<data>
		vIHIP4Ouf2QfO2mOySw2B0/84io=
		</data>
		<key>Headers/ATStressTestItem.h</key>
		<data>
		I1+0P2HalQlAz7Tb5lV3UcRNdZU=
		</data>
		<key>Headers/ATSyncSetting.h</key>
		<data>
		VZXFkZiEq6oL1EF+wKN0ROGIpec=
		</data>
		<key>Headers/ATTargetItem.h</key>
		<data>
		NO5pLP/NlVBRSBxQpoSoLR7Wc0k=
		</data>
		<key>Headers/ATWatchFaceElement.h</key>
		<data>
		RtS73U8e7nRnpRYJTQcyZv39M/g=
		</data>
		<key>Headers/ATWeatherItem.h</key>
		<data>
		oSzg65pRHP3mvLgbKH9WBsWZB6I=
		</data>
		<key>Headers/ATWorkingItem.h</key>
		<data>
		pABBP49nIKrIMDNWOuvt/Yzp9/o=
		</data>
		<key>Headers/AWDeviceData.h</key>
		<data>
		M0cewYpUtOzk+ea05T523gi71ho=
		</data>
		<key>Headers/IBCoreProfiles.h</key>
		<data>
		x1GyvniJobRz3NTCXpGqhq5PuXU=
		</data>
		<key>Headers/IBDeviceProtocol.h</key>
		<data>
		1G7qRZ4m9ENTLYCckEzSwXroU2k=
		</data>
		<key>Headers/IBDeviceSetting.h</key>
		<data>
		ohsmzY0O2aknUY9ACT6QKgjWiZY=
		</data>
		<key>Headers/IBGattDevices.h</key>
		<data>
		Zu4wfknjHNc+f2ABWiCTVOKzA4g=
		</data>
		<key>Headers/IBMutableArray.h</key>
		<data>
		waBPjIJYE+jSiS6jqJ97PWhmN94=
		</data>
		<key>Headers/ICByteUtils.h</key>
		<data>
		+Wqgq0Ayp7nMPGApRT38qLSxh6M=
		</data>
		<key>Headers/LSBluetoothManager+Pair.h</key>
		<data>
		1hdiH9OnzUxfAhMtXkzPz/PFRhc=
		</data>
		<key>Headers/LSBluetoothManager+Push.h</key>
		<data>
		xZ9DFWVHpAvxUlaYKmtkvX1d1R0=
		</data>
		<key>Headers/LSBluetoothManager+Sync.h</key>
		<data>
		gTkm7UmQMQyRE9UO2u8Ti81B8pk=
		</data>
		<key>Headers/LSBluetoothManager.h</key>
		<data>
		FXQxb6T/8rF91C2IiAakgCJ8rTY=
		</data>
		<key>Headers/LSBluetoothManagerProfiles.h</key>
		<data>
		CPqys1QLNHvu3hyeIX1EUmpoJpg=
		</data>
		<key>Headers/LSBluetoothPlugin.h</key>
		<data>
		J3RuLmbN2P6r0GgVf0f9DOG2fb8=
		</data>
		<key>Headers/LSBluetoothStatusDelegate.h</key>
		<data>
		GesSCjScwTKkNZs7UP1fGwHHgyA=
		</data>
		<key>Headers/LSDeviceData.h</key>
		<data>
		HzNjGw8mMeCKNLrQ601S2Is7VD4=
		</data>
		<key>Headers/LSDeviceDataDelegate.h</key>
		<data>
		m+C7Nh7hl8A82Vj+iPq0+QGDTl0=
		</data>
		<key>Headers/LSDeviceFeature.h</key>
		<data>
		sefUupDm07SLE3mrcQmkWlU0WQ4=
		</data>
		<key>Headers/LSDeviceFilterInfo.h</key>
		<data>
		kQa7IZkYRLlfkQ+IVS7j/25MANQ=
		</data>
		<key>Headers/LSDeviceInfo.h</key>
		<data>
		9FQw8q83YLZu9RzQrgK/WhAaeXY=
		</data>
		<key>Headers/LSDeviceOtaMsg.h</key>
		<data>
		myY71JL/o8NugZl1lx+XRnl0iZA=
		</data>
		<key>Headers/LSDevicePairMsg.h</key>
		<data>
		t+zekZOja/AiKw9TmVe0q+dXZ9E=
		</data>
		<key>Headers/LSDevicePairingDelegate.h</key>
		<data>
		pGbX3PzhE6HSrWSpyEokhtYfYsY=
		</data>
		<key>Headers/LSDeviceUpgradingDelegate.h</key>
		<data>
		7MC86JeOz65PbawJsmRRugxOnVk=
		</data>
		<key>Headers/LSNotificationMsg.h</key>
		<data>
		3NhtzllQbIyo+FDebI2e1irMviw=
		</data>
		<key>Headers/LSUserInfo.h</key>
		<data>
		eWwcxhISLw+WWIrPuq5oqzDpRUI=
		</data>
		<key>Headers/NSObject+Json.h</key>
		<data>
		DEJ9bk+WVYGwq+7714Aeio/R0mY=
		</data>
		<key>Headers/WFTemplate.h</key>
		<data>
		tNcYfzyR7QcA7SIWZUxwyMowxA4=
		</data>
		<key>Info.plist</key>
		<data>
		6H9erjjby2CS00h3xgqcjgv+kLk=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		aKc/KRnHYwJKyMbsleKDXuZu4XA=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/ATAlarmClockItem.h</key>
		<dict>
			<key>hash</key>
			<data>
			+8n/IPJU6XXJOK8LG7IybmKG5Hw=
			</data>
			<key>hash2</key>
			<data>
			sTlCTUl8eM+9zDg1OeoqfpF7s+2OXIpAPUQ5lKJv3eo=
			</data>
		</dict>
		<key>Headers/ATAutoRecognitionItem.h</key>
		<dict>
			<key>hash</key>
			<data>
			74c9QduCL+tt1sI+LsNaEYJKUQI=
			</data>
			<key>hash2</key>
			<data>
			855nlpzbcgoDCArX4pd0qt23O44VuGlzaEQx+D0GC6o=
			</data>
		</dict>
		<key>Headers/ATBatteryInfo.h</key>
		<dict>
			<key>hash</key>
			<data>
			olG3YKQkFueC2bwDdBjOiiwSa+s=
			</data>
			<key>hash2</key>
			<data>
			hepp8gmSElvmG9Fn0PC25Ppw3Hms9MiyuQOTnO0gGUc=
			</data>
		</dict>
		<key>Headers/ATBloodOxygenItem.h</key>
		<dict>
			<key>hash</key>
			<data>
			Go2nHECr00GmM3Q2e7IjQIS918s=
			</data>
			<key>hash2</key>
			<data>
			K5xdoY5qzZqbsrAFoYf1amFulDCpryklGiz77vArO5w=
			</data>
		</dict>
		<key>Headers/ATBuriedPointItem.h</key>
		<dict>
			<key>hash</key>
			<data>
			AuvlvmXIUptlllEk3nqb0BWPCac=
			</data>
			<key>hash2</key>
			<data>
			7tKV+44T0FSACe2X3WxU/kMnkoYlZ7uXzZ+2reHFRCY=
			</data>
		</dict>
		<key>Headers/ATCavoProfiles.h</key>
		<dict>
			<key>hash</key>
			<data>
			0zbUU2SDibVKKNFouq5+O8fVip8=
			</data>
			<key>hash2</key>
			<data>
			67j0Bxz24En5eLt/j74Y89gHfkWqwyUxUiuk2ExK+5Y=
			</data>
		</dict>
		<key>Headers/ATConfigItem.h</key>
		<dict>
			<key>hash</key>
			<data>
			qT2W/siw3GwGFvFxLGfVtG0DHXs=
			</data>
			<key>hash2</key>
			<data>
			97+Rpi4CXLC9vCJwdtoA+7e8pD+/WBYI5+9uDcZAxh4=
			</data>
		</dict>
		<key>Headers/ATDataProfiles.h</key>
		<dict>
			<key>hash</key>
			<data>
			JKxya4GUI81xKPKgubx8ey0CVa4=
			</data>
			<key>hash2</key>
			<data>
			a5aFDL5KfUZK/UzTTUGNuK+G57PTjdI7r0aQB949dz4=
			</data>
		</dict>
		<key>Headers/ATDeviceData.h</key>
		<dict>
			<key>hash</key>
			<data>
			vRBuctYE5W6rZPl7hvXRugX91YU=
			</data>
			<key>hash2</key>
			<data>
			23XN8xv90Ob0tPYM4WxGtOE/zfVwPHQKVA5Dd4GPQog=
			</data>
		</dict>
		<key>Headers/ATDisturbItem.h</key>
		<dict>
			<key>hash</key>
			<data>
			tPk15xJJcC/XOL9LKMnBLQI9udQ=
			</data>
			<key>hash2</key>
			<data>
			kafTNTOGsmUqcgirHkEa8ioiUoPog+73yPb43pZ7eng=
			</data>
		</dict>
		<key>Headers/ATEventClockItem.h</key>
		<dict>
			<key>hash</key>
			<data>
			4XF5qVvqkdEi56NS529pS6xwk70=
			</data>
			<key>hash2</key>
			<data>
			Lz9rS/pMF9OaeNVEfDJwtkGvA37PM7wKI+Il8Wh3vAg=
			</data>
		</dict>
		<key>Headers/ATExerciseStatus.h</key>
		<dict>
			<key>hash</key>
			<data>
			gws0oypEIRbz9JlMhY933ve6LvQ=
			</data>
			<key>hash2</key>
			<data>
			IX0uWdcSNKw4y/Vt5qjDG35qhzm2MCvVXGkXNMs6up4=
			</data>
		</dict>
		<key>Headers/ATFileData.h</key>
		<dict>
			<key>hash</key>
			<data>
			AtXJ+xk9gM5J+T6/RDhauYj4DhY=
			</data>
			<key>hash2</key>
			<data>
			W7VvlvIkU+hTeH5kDCgCYsARRcPpxdZN3hJhQ/E6FaA=
			</data>
		</dict>
		<key>Headers/ATFileUpdateMsg.h</key>
		<dict>
			<key>hash</key>
			<data>
			k1k002WEWGlB6RCkDbt87UxnH6E=
			</data>
			<key>hash2</key>
			<data>
			DC/Y+WMow5SoMBSR71RJrUbrVXthYFN74wGQNaxqR/8=
			</data>
		</dict>
		<key>Headers/ATGpsExerciseItem.h</key>
		<dict>
			<key>hash</key>
			<data>
			Yrkx6osk3Gtg2sT4r3NkJ5yStQk=
			</data>
			<key>hash2</key>
			<data>
			8JhrK6byjxLDEp34Uewa/xF/Q6GZrkiXND1RMqZxJe0=
			</data>
		</dict>
		<key>Headers/ATHeartRateItem.h</key>
		<dict>
			<key>hash</key>
			<data>
			jl5iilx+ugSpdU/YyE1jlM7UUDo=
			</data>
			<key>hash2</key>
			<data>
			IdmBVLgau7+tc7nIwxfKhBn2eq5iUUrAwvXM9jN2d4Y=
			</data>
		</dict>
		<key>Headers/ATHeartRateZoneItem.h</key>
		<dict>
			<key>hash</key>
			<data>
			64E6WRcAuUcCND8ViaXAm2Qnvhc=
			</data>
			<key>hash2</key>
			<data>
			mtxUBb2MRbMhNCaH9v3QhU5+/QWOwmlOi9GIUmbZpeU=
			</data>
		</dict>
		<key>Headers/ATIotDevice.h</key>
		<dict>
			<key>hash</key>
			<data>
			JggoG4saLWc3cP2hyfdrnVbcDzI=
			</data>
			<key>hash2</key>
			<data>
			GzT2P5XjldyYQ53zrR8FCT/VsoILGDpSuIoSUU5XjeI=
			</data>
		</dict>
		<key>Headers/ATIotInfo.h</key>
		<dict>
			<key>hash</key>
			<data>
			x5z7d1GeJuCC7hX3kCQuu3CDn44=
			</data>
			<key>hash2</key>
			<data>
			QL6mrlAU4sDdT/305Ai7r71JdfpoUC6vrOUOfXSRuNo=
			</data>
		</dict>
		<key>Headers/ATLogItem.h</key>
		<dict>
			<key>hash</key>
			<data>
			zINgGIaroZ3QMh3fxH3DlvXsn2I=
			</data>
			<key>hash2</key>
			<data>
			q6nDX47RsA+naf5yys8yg7qHGmobFHNW0TS1PMZCZHM=
			</data>
		</dict>
		<key>Headers/ATMeditationItem.h</key>
		<dict>
			<key>hash</key>
			<data>
			dNxedsw64Rn8OC96oDXOFalDL4c=
			</data>
			<key>hash2</key>
			<data>
			UYoyT+acJj8qocPjHWLqrI3wtLoKaMNt20tKos8V8wU=
			</data>
		</dict>
		<key>Headers/ATMoodItem.h</key>
		<dict>
			<key>hash</key>
			<data>
			n9P4LFraOTtDj4PWgXtxSDoRBhs=
			</data>
			<key>hash2</key>
			<data>
			HHRgevAZvaA+azIgWmm0cZR95veMLoBAr/xK6mmri9k=
			</data>
		</dict>
		<key>Headers/ATPairSetting.h</key>
		<dict>
			<key>hash</key>
			<data>
			m7EMybzuBkfRloTlIemSVD7TQ2s=
			</data>
			<key>hash2</key>
			<data>
			yflH+TLIkIsb0gpUu6QbnWiTd0k2knXDrg6YjJqGsTI=
			</data>
		</dict>
		<key>Headers/ATReminderItem.h</key>
		<dict>
			<key>hash</key>
			<data>
			sQ2zOdCCOyLnyuzv19SOL+lpvoo=
			</data>
			<key>hash2</key>
			<data>
			gwspcuJTME8QJ5ZjS8dHBaB1SCdkrX5bI7e3K0RRGNU=
			</data>
		</dict>
		<key>Headers/ATSedentaryItem.h</key>
		<dict>
			<key>hash</key>
			<data>
			azA05mbb57VOeJ84Nd+2bcRwnKQ=
			</data>
			<key>hash2</key>
			<data>
			eC4YlylfCq6xl0YcdrZfhW65RRdEVskEVNf+6U8M1/Y=
			</data>
		</dict>
		<key>Headers/ATSensorItem.h</key>
		<dict>
			<key>hash</key>
			<data>
			MwRik3RKKJfL0qgaKT1I5u0yom4=
			</data>
			<key>hash2</key>
			<data>
			gjkiQf+duLHtQpLLda/UvS9ypsJUnMGnw15KL0jpoo4=
			</data>
		</dict>
		<key>Headers/ATSettingProfiles.h</key>
		<dict>
			<key>hash</key>
			<data>
			fvYOC902YEXZPok12dNjSfqJxYM=
			</data>
			<key>hash2</key>
			<data>
			Bd5/va0o30CDuo1aQ8HviQWEchZm1Q6UAv734fNT26s=
			</data>
		</dict>
		<key>Headers/ATSleepReportItem.h</key>
		<dict>
			<key>hash</key>
			<data>
			2nG695GB5Zs/dzAGOohd+w9f4fU=
			</data>
			<key>hash2</key>
			<data>
			6SC5HkdBZRDuPqplHaqjTitgXxTl4tHcFcNqpTQRGQ8=
			</data>
		</dict>
		<key>Headers/ATStepItem.h</key>
		<dict>
			<key>hash</key>
			<data>
			vIHIP4Ouf2QfO2mOySw2B0/84io=
			</data>
			<key>hash2</key>
			<data>
			eshUsDCc2hjMKQ6n6Dvb6zKfatrBP6Gsgu0BJQS+nX4=
			</data>
		</dict>
		<key>Headers/ATStressTestItem.h</key>
		<dict>
			<key>hash</key>
			<data>
			I1+0P2HalQlAz7Tb5lV3UcRNdZU=
			</data>
			<key>hash2</key>
			<data>
			khjf1RVcG/HIN2W9WVWoepsi1Xueb2ORUX8Mh/TqPfg=
			</data>
		</dict>
		<key>Headers/ATSyncSetting.h</key>
		<dict>
			<key>hash</key>
			<data>
			VZXFkZiEq6oL1EF+wKN0ROGIpec=
			</data>
			<key>hash2</key>
			<data>
			5ktn28i0J+C+jh8mXYvjRn0ZCTd2we5QTnG7l2iKZiA=
			</data>
		</dict>
		<key>Headers/ATTargetItem.h</key>
		<dict>
			<key>hash</key>
			<data>
			NO5pLP/NlVBRSBxQpoSoLR7Wc0k=
			</data>
			<key>hash2</key>
			<data>
			4jMXFYJ5/s0USZaEPFFBQme9CsutpAADIHYS2bYnvDM=
			</data>
		</dict>
		<key>Headers/ATWatchFaceElement.h</key>
		<dict>
			<key>hash</key>
			<data>
			RtS73U8e7nRnpRYJTQcyZv39M/g=
			</data>
			<key>hash2</key>
			<data>
			w24A9WYXhcl4Fm25hDfjp/YA6Z1Fy75mVZwdvXH5F4s=
			</data>
		</dict>
		<key>Headers/ATWeatherItem.h</key>
		<dict>
			<key>hash</key>
			<data>
			oSzg65pRHP3mvLgbKH9WBsWZB6I=
			</data>
			<key>hash2</key>
			<data>
			DrAIiInZhZ10miNrTpJ0cIwchkdieaxvJeYX8CwkkmY=
			</data>
		</dict>
		<key>Headers/ATWorkingItem.h</key>
		<dict>
			<key>hash</key>
			<data>
			pABBP49nIKrIMDNWOuvt/Yzp9/o=
			</data>
			<key>hash2</key>
			<data>
			o0df5NUGOgviQE4uKS4MLfSo3NOitBveM4sIpBA6vYc=
			</data>
		</dict>
		<key>Headers/AWDeviceData.h</key>
		<dict>
			<key>hash</key>
			<data>
			M0cewYpUtOzk+ea05T523gi71ho=
			</data>
			<key>hash2</key>
			<data>
			HlGo7CBvobfyM20Hngmb+hRjJ8/G6KCBxuqpKu3lBbE=
			</data>
		</dict>
		<key>Headers/IBCoreProfiles.h</key>
		<dict>
			<key>hash</key>
			<data>
			x1GyvniJobRz3NTCXpGqhq5PuXU=
			</data>
			<key>hash2</key>
			<data>
			1MHrNHrv7SnpGecL79anvjNfDEMV9lLvtmZj+8MWVcQ=
			</data>
		</dict>
		<key>Headers/IBDeviceProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			1G7qRZ4m9ENTLYCckEzSwXroU2k=
			</data>
			<key>hash2</key>
			<data>
			n1OUUeuKSnoUAGsKnOu6ySGk7oM4aNCHD+wCvLRnExE=
			</data>
		</dict>
		<key>Headers/IBDeviceSetting.h</key>
		<dict>
			<key>hash</key>
			<data>
			ohsmzY0O2aknUY9ACT6QKgjWiZY=
			</data>
			<key>hash2</key>
			<data>
			uPHPNvU+MpzxBErB4uVATF1ncdXODYo14fBwqwEG96g=
			</data>
		</dict>
		<key>Headers/IBGattDevices.h</key>
		<dict>
			<key>hash</key>
			<data>
			Zu4wfknjHNc+f2ABWiCTVOKzA4g=
			</data>
			<key>hash2</key>
			<data>
			Lg9FTDZ+77Timr0AS2qeArapxpMsJMplhEIUxSUe3Gw=
			</data>
		</dict>
		<key>Headers/IBMutableArray.h</key>
		<dict>
			<key>hash</key>
			<data>
			waBPjIJYE+jSiS6jqJ97PWhmN94=
			</data>
			<key>hash2</key>
			<data>
			X1z8rBI5n/ouzaaV/rQ7+sgjGx1KYGghMNnZnikN4Gk=
			</data>
		</dict>
		<key>Headers/ICByteUtils.h</key>
		<dict>
			<key>hash</key>
			<data>
			+Wqgq0Ayp7nMPGApRT38qLSxh6M=
			</data>
			<key>hash2</key>
			<data>
			oTvlITQeXX0qe7zvMAT9g1qMGigmOtSiav1Loogjwls=
			</data>
		</dict>
		<key>Headers/LSBluetoothManager+Pair.h</key>
		<dict>
			<key>hash</key>
			<data>
			1hdiH9OnzUxfAhMtXkzPz/PFRhc=
			</data>
			<key>hash2</key>
			<data>
			hZ9lFAicBk/LNu1caD+fuqpSynmcXXKe/3YWJT2ls5M=
			</data>
		</dict>
		<key>Headers/LSBluetoothManager+Push.h</key>
		<dict>
			<key>hash</key>
			<data>
			xZ9DFWVHpAvxUlaYKmtkvX1d1R0=
			</data>
			<key>hash2</key>
			<data>
			EEO1QWok5VPVnvHeLAtikBSwXCY3BXr2Gl17Gg8c1jU=
			</data>
		</dict>
		<key>Headers/LSBluetoothManager+Sync.h</key>
		<dict>
			<key>hash</key>
			<data>
			gTkm7UmQMQyRE9UO2u8Ti81B8pk=
			</data>
			<key>hash2</key>
			<data>
			ltlBGblcqcPeawPqfMUrtv6IwZTEDdmc4jxWjD8s3kE=
			</data>
		</dict>
		<key>Headers/LSBluetoothManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			FXQxb6T/8rF91C2IiAakgCJ8rTY=
			</data>
			<key>hash2</key>
			<data>
			rze2dCIkgXPDs4qPc2QdgXcxNWkPUjYkYTSr8oGj0HE=
			</data>
		</dict>
		<key>Headers/LSBluetoothManagerProfiles.h</key>
		<dict>
			<key>hash</key>
			<data>
			CPqys1QLNHvu3hyeIX1EUmpoJpg=
			</data>
			<key>hash2</key>
			<data>
			G/izfZryMoay+hGlVhMtedgTj1Jn+PpfaVa7XgIpUlM=
			</data>
		</dict>
		<key>Headers/LSBluetoothPlugin.h</key>
		<dict>
			<key>hash</key>
			<data>
			J3RuLmbN2P6r0GgVf0f9DOG2fb8=
			</data>
			<key>hash2</key>
			<data>
			T9SkSlQ7SQ/kDJBJc9+jfnxl0qemyeizXoeLPdEqBn4=
			</data>
		</dict>
		<key>Headers/LSBluetoothStatusDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			GesSCjScwTKkNZs7UP1fGwHHgyA=
			</data>
			<key>hash2</key>
			<data>
			w7AYbBMFOz/uZogZ26GbER4jOUaB4WiNpkjYli3sB54=
			</data>
		</dict>
		<key>Headers/LSDeviceData.h</key>
		<dict>
			<key>hash</key>
			<data>
			HzNjGw8mMeCKNLrQ601S2Is7VD4=
			</data>
			<key>hash2</key>
			<data>
			AA9KnkQ6PmpPkDc4pJMpqiZmBrzogr783fWUpP8EvS4=
			</data>
		</dict>
		<key>Headers/LSDeviceDataDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			m+C7Nh7hl8A82Vj+iPq0+QGDTl0=
			</data>
			<key>hash2</key>
			<data>
			aZDIUOFc8aSI4/zEqYoch1ktU3LbfGuR2+sVYkr+/fU=
			</data>
		</dict>
		<key>Headers/LSDeviceFeature.h</key>
		<dict>
			<key>hash</key>
			<data>
			sefUupDm07SLE3mrcQmkWlU0WQ4=
			</data>
			<key>hash2</key>
			<data>
			KQ+D81rftQh3ySs/NftVH/KbJ2SihLCJfipcelMGtQ0=
			</data>
		</dict>
		<key>Headers/LSDeviceFilterInfo.h</key>
		<dict>
			<key>hash</key>
			<data>
			kQa7IZkYRLlfkQ+IVS7j/25MANQ=
			</data>
			<key>hash2</key>
			<data>
			sQg+zKbtRCtxHtC9q8Aoe624CIeVlWNs26nrwXMmiHg=
			</data>
		</dict>
		<key>Headers/LSDeviceInfo.h</key>
		<dict>
			<key>hash</key>
			<data>
			9FQw8q83YLZu9RzQrgK/WhAaeXY=
			</data>
			<key>hash2</key>
			<data>
			ShtQsZazzjKMxly5EjS49ryC3vFe3vAdVDp+eoJMAms=
			</data>
		</dict>
		<key>Headers/LSDeviceOtaMsg.h</key>
		<dict>
			<key>hash</key>
			<data>
			myY71JL/o8NugZl1lx+XRnl0iZA=
			</data>
			<key>hash2</key>
			<data>
			3WfTRzld5S5PEJOUPPb9Bd+W7w5+XUqx0ZIhWjwP3Og=
			</data>
		</dict>
		<key>Headers/LSDevicePairMsg.h</key>
		<dict>
			<key>hash</key>
			<data>
			t+zekZOja/AiKw9TmVe0q+dXZ9E=
			</data>
			<key>hash2</key>
			<data>
			g4ssvfUYWIoZilV0PxAyd/bpxg/Yb/vVEJDFT3nhU+8=
			</data>
		</dict>
		<key>Headers/LSDevicePairingDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			pGbX3PzhE6HSrWSpyEokhtYfYsY=
			</data>
			<key>hash2</key>
			<data>
			TROdWcYvSL99anS8vjuLKozQW9L2unYbv64FLTm2TEM=
			</data>
		</dict>
		<key>Headers/LSDeviceUpgradingDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			7MC86JeOz65PbawJsmRRugxOnVk=
			</data>
			<key>hash2</key>
			<data>
			9N8lF+cbSiEixvwYOMNBKUoV+MDNy5XbVGi8jI+yVqg=
			</data>
		</dict>
		<key>Headers/LSNotificationMsg.h</key>
		<dict>
			<key>hash</key>
			<data>
			3NhtzllQbIyo+FDebI2e1irMviw=
			</data>
			<key>hash2</key>
			<data>
			P/RxVyRCceadTay9HrNGI8Lh8c5VezZIPnv9jIWOtcc=
			</data>
		</dict>
		<key>Headers/LSUserInfo.h</key>
		<dict>
			<key>hash</key>
			<data>
			eWwcxhISLw+WWIrPuq5oqzDpRUI=
			</data>
			<key>hash2</key>
			<data>
			sPuEKYdLpXY3Xj5vYT9//934JGrsccHl+MJP/Kid2kc=
			</data>
		</dict>
		<key>Headers/NSObject+Json.h</key>
		<dict>
			<key>hash</key>
			<data>
			DEJ9bk+WVYGwq+7714Aeio/R0mY=
			</data>
			<key>hash2</key>
			<data>
			Z4AdLk5FQWtlYb7KgcWCW7sEMNPzh3JtW9AsAJ0fz3s=
			</data>
		</dict>
		<key>Headers/WFTemplate.h</key>
		<dict>
			<key>hash</key>
			<data>
			tNcYfzyR7QcA7SIWZUxwyMowxA4=
			</data>
			<key>hash2</key>
			<data>
			Hyzz+UJUq6ib4Io9DQZrHsAfcaOs6i6OoJMRK+v5yhs=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			aKc/KRnHYwJKyMbsleKDXuZu4XA=
			</data>
			<key>hash2</key>
			<data>
			1dVRGNxqqyryo//Nq1hXsOiOQQ5BfmTpyQLqn/wT0wo=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
