//
//  LSBluetoothPlugin.h
//  LSBluetoothPlugin
//
//  Created by caichixiang on 2020/6/30.
//  Copyright © 2020 sky. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "LSBluetoothManager.h"
#import "LSBluetoothManager+Push.h"
#import "LSBluetoothManager+Sync.h"
#import "LSBluetoothManager+Pair.h"

//! Project version number for LSBluetoothPlugin.
FOUNDATION_EXPORT double LSBluetoothPluginVersionNumber;

//! Project version string for LSBluetoothPlugin.
FOUNDATION_EXPORT const unsigned char LSBluetoothPluginVersionString[];

// In this header, you should import all the public headers of your framework using statements like #import <LSBluetoothPlugin/PublicHeader.h>


#pragma mark - Current Verion 2.0.3

/**
 * SDK 支持的系统架构
 *  armv7,
 *  armv7s,
 *  arm64,
 *  arm64e,
 */
