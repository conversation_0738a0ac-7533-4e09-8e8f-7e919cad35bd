import Flutter
import UIKit
import AudioToolbox

@main
@objc class AppDelegate: FlutterAppDelegate {
    private var methodChannel: FlutterMethodChannel?
    private var microscopeChannel: FlutterMethodChannel?
    private var soundChannel: FlutterMethodChannel?

    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        GeneratedPluginRegistrant.register(with: self)
        
        guard let controller = window?.rootViewController as? FlutterViewController else {
            return super.application(application, didFinishLaunchingWithOptions: launchOptions)
        }

        // Existing Bluetooth channel setup
        setupBluetoothChannel(controller: controller)

        // Microscope channel setup
        setupMicroscopeChannel(controller: controller)

        // Sound channel setup
        setupSoundChannel(controller: controller)
        
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }

    // Add this method for Bluetooth channel setup
    private func setupBluetoothChannel(controller: FlutterViewController) {
        methodChannel = FlutterMethodChannel(
            name: "com.telehealth.bluetooth/channel",
            binaryMessenger: controller.binaryMessenger
        )
        
        setupMethodCallHandler()
        setupBluetoothResponseHandler()
    }

    private func setupMicroscopeChannel(controller: FlutterViewController) {
        microscopeChannel = FlutterMethodChannel(
            name: "com.telehealth.microscope/channel",
            binaryMessenger: controller.binaryMessenger
        )
        
        MicroScopeService.shared.methodChannel = microscopeChannel
        
        microscopeChannel?.setMethodCallHandler { (call, result) in
            switch call.method {
            case "captureImage":
                MicroScopeService.shared.captureImage()
                result(nil)
                
            case "refreshConnection":
                MicroScopeService.shared.refreshConnection()
                result(nil)
                
            case "completeReset":
                // Force a more complete reset of the microscope service
                MicroScopeService.shared.completeReset()
                result(nil)
                
            default:
                result(FlutterMethodNotImplemented)
            }
        }
    }


    private func setupMethodCallHandler() {
        methodChannel?.setMethodCallHandler { [weak self] (call, result) in
            guard let self = self else {
                result(FlutterError(code: "UNAVAILABLE", message: "Instance not available", details: nil))
                return
            }

            switch call.method {
            case "startScan":
                let success = BluetoothService.shared.startScan()
                result(success)

            case "stopScan":
                let success = BluetoothService.shared.stopScan()
                result(success)

            case "pairDevice":
                self.handlePairDevice(call: call, result: result)

            case "startDataSync":
                let success = BluetoothService.shared.startDataSync()
                result(success) 

            case "getFoundDevices":
                self.handleGetFoundDevices(result: result)
                
            case "getCurrentBluetoothState":
                let state = BluetoothService.shared.getCurrentBluetoothStateValue()
                result(state)


            default:
                result(FlutterMethodNotImplemented)
            }
        }
    }

    private func handlePairDevice(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let mac = args["mac"] as? String,
              let broadcastId = args["broadcastId"] as? String,
              let typeRaw = args["type"] as? String,
              let deviceType = LSDeviceType(rawValue: UInt(typeRaw) ?? 0),
              let protocolType = args["protocolType"] as? String else {
            result(FlutterError(code: "INVALID_ARGUMENT", message: "Missing required parameters", details: nil))
            return
        }
        
        let device = LSDeviceInfo()
        device.macAddress = mac
        device.broadcastId = broadcastId
        device.deviceType = deviceType
        device.protocolType = protocolType

        let success = BluetoothService.shared.pairDevice(device)
        result(success)
    }

    private func handleGetFoundDevices(result: @escaping FlutterResult) {
        let devices = BluetoothService.shared.foundDevices.map { BluetoothService.shared.deviceToDict($0) }
        result(devices)
    }

    private func setupSoundChannel(controller: FlutterViewController) {
        soundChannel = FlutterMethodChannel(
            name: "com.telehealth.sound/channel",
            binaryMessenger: controller.binaryMessenger
        )

        soundChannel?.setMethodCallHandler { (call, result) in
            switch call.method {
            case "playShutterSound":
                AudioServicesPlaySystemSound(SystemSoundID(1108)) // 1108 = camera shutter
                result(nil)

            default:
                result(FlutterMethodNotImplemented)
            }
        }
    }

    private func setupBluetoothResponseHandler() {
        BluetoothService.shared.flutterResponseHandler = { [weak self] method, data in
            guard let self = self else { return }
            self.methodChannel?.invokeMethod(method, arguments: data)
        }
    }
}


