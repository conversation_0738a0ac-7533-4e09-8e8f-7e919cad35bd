import Foundation
import LSBluetoothPlugin
import CoreBluetooth

class BluetoothService: NSObject {
    static let shared = BluetoothService()
    private var manager: LSBluetoothManager?
    private(set) var foundDevices = [LSDeviceInfo]()
    var flutterResponseHandler: ((String, Any?) -> Void)?
    private var bluetoothState: CBManagerState = .unknown {
        didSet {
            print("Bluetooth state updated to: \(bluetoothState.rawValue)")
        }
    }

    override init() {
        super.init()
        print("DEBUG: Initializing BluetoothService")
        initializeSDK()
    }

    // MARK: - SDK Initialization
    private func initializeSDK() {
        print("DEBUG: Initializing SDK")
        manager = LSBluetoothManager.default()
        manager?.initManager(withDispatch: DispatchQueue(label: "com.telehealth.bluetooth"))
        manager?.saveDebugMessage(true, forFileDirectory: "")
        manager?.checkingBluetoothStatus(self)
        print("DEBUG: SDK initialization complete")
    }

    func isBluetoothAvailable() -> Bool {
        print("DEBUG: Checking Bluetooth availability: \(bluetoothState == .poweredOn)")
        return bluetoothState == .poweredOn
    }
    
    func getCurrentBluetoothStateValue() -> Int {
        switch bluetoothState {
        case .poweredOn: return 5
        case .poweredOff: return 4
        case .unsupported: return 2
        case .unauthorized: return 3
        case .resetting: return 1
        case .unknown, _: return 0
        }
    }

    func startScan() -> Bool {
            guard let manager = manager else {
                sendToFlutter(method: "error", data: ["message": "SDK not initialized"])
                return false
            }
            
            guard bluetoothState == .poweredOn else {
                sendToFlutter(method: "error", data: ["message": "Bluetooth is turned off"])
                return false
            }
            
            foundDevices.removeAll()
            let scanStarted = manager.searchDevice(nil) { [weak self] device in
            guard let self = self, let device = device else {
                print("DEBUG: Device callback returned nil")
                return
            }
            
            print("DEBUG: Device found: \(device.deviceName ?? "Unknown") - MAC: \(device.macAddress ?? "Unknown")")
            
            if !self.foundDevices.contains(where: { $0.macAddress == device.macAddress }) {
                self.foundDevices.append(device)
                let deviceDict = self.deviceToDict(device)
                print("DEBUG: Adding new device to list: \(deviceDict)")
                self.sendToFlutter(method: "deviceFound", data: deviceDict)
            }
        }
        
        if !scanStarted {
            print("DEBUG: Failed to start scanning")
            sendToFlutter(method: "error", data: [
                "message": "Failed to start scanning",
                "code": "SCAN_FAILED"
            ])
        } else {
            print("DEBUG: Scan started successfully")
        }
        
        return scanStarted
    }

    func stopScan() -> Bool {
            guard manager != nil else { return false }
            manager?.stopSearch()
            return true
        }
    

    // MARK: - Device Pairing
    func pairDevice(_ device: LSDeviceInfo) -> Bool {
            guard let manager = manager else { return false }
            if let broadcastId = device.broadcastId,
               let existingDevice = manager.deviceMap[broadcastId] as? LSDeviceInfo {
                manager.deleteDevice(existingDevice.broadcastId ?? "")
            }
            
            return manager.pairDevice(device, delegate: self)
        }

    // MARK: - Data Sync
    func startDataSync() -> Bool {
            guard let manager = manager, manager.deviceMap.count > 0 else {
                sendToFlutter(method: "error", data: ["message": "No devices registered"])
                return false
            }
            manager.dataDelegate = self
            return manager.startDeviceSync(self)
        }

    

    // MARK: - Helper Functions
    func deviceToDict(_ device: LSDeviceInfo?) -> [String: Any] {
        guard let device = device else { return [:] }
        let deviceDict: [String: Any] = [
            "name": device.deviceName ?? "",
            "mac": device.macAddress ?? "",
            "broadcastId": device.broadcastId ?? "",
            "type": String(device.deviceType.rawValue),
            "protocolType": device.protocolType ?? "",
            "battery": device.battery,
            "isConnected": (manager?.checkConnectState(device.broadcastId ?? "") ?? .disconnect) == .connected,
            "heartRate": device.heartRate,
            "maxUserQuantity": device.maxUserQuantity,
            "deviceUserNumber": device.deviceUserNumber,
            "isUpgrading": device.isUpgrading
        ]
        print("DEBUG: Device dictionary created: \(deviceDict)")
        return deviceDict
    }

    func addDeviceToSyncList(_ device: LSDeviceInfo) {
        print("DEBUG: Adding device to sync list: \(device.deviceName ?? "Unknown")")
        if manager?.addDevice(device) ?? false {
            print("DEBUG: Device added to sync list successfully")
            sendToFlutter(method: "deviceAdded", data: deviceToDict(device))
        } else {
            print("DEBUG: Failed to add device to sync list")
            sendToFlutter(method: "error", data: [
                "message": "Failed to add device",
                "code": "DEVICE_ADD_FAILED"
            ])
        }
    }

    func removeDevice(_ broadcastId: String) {
        print("DEBUG: Removing device with broadcast ID: \(broadcastId)")
        guard let manager = manager else { return }

        if manager.deleteDevice(broadcastId) {
            print("DEBUG: Device removed successfully")
            sendToFlutter(method: "deviceRemoved", data: ["broadcastId": broadcastId])
        } else {
            print("DEBUG: Failed to remove device")
            sendToFlutter(method: "error", data: [
                "message": "Failed to remove device",
                "code": "DEVICE_REMOVE_FAILED"
            ])
        }
    }

    private func sendToFlutter(method: String, data: Any?) {
        print("DEBUG: Sending to Flutter -> Method: \(method), Data: \(String(describing: data))")
        DispatchQueue.main.async { [weak self] in
            self?.flutterResponseHandler?(method, data)
        }
    }
}

// MARK: - Bluetooth Status Delegate
extension BluetoothService: LSBluetoothStatusDelegate {
    func systemDidBluetoothStatusChange(_ bleState: CBManagerState) {
        print("DEBUG: System Bluetooth status changed to: \(bleState)")
        self.bluetoothState = bleState
        let stateValue: Int
        switch bleState {
        case .poweredOn: stateValue = 5
        case .poweredOff: stateValue = 4
        case .unsupported: stateValue = 2
        case .unauthorized: stateValue = 3
        case .resetting: stateValue = 1
        case .unknown: fallthrough
        @unknown default: stateValue = 0
        }
        sendToFlutter(method: "systemBluetoothStatus", data: ["state": stateValue])
    }

    func bleManager(_ manager: LSBluetoothManager!, didUpdateState state: Int) {
        print("DEBUG: BLE Manager state updated to: \(state)")
        sendToFlutter(method: "bluetoothStatus", data: ["state": state])
    }
}

// MARK: - Pairing Delegate
extension BluetoothService: LSDevicePairingDelegate {
    func bleDevice(_ device: LSDeviceInfo!, didPairStateChanged state: LSPairState) {
        print("DEBUG: Pairing state changed for device \(device.deviceName ?? "Unknown"): \(state.rawValue)")
        var stateDict: [String: Any] = [
            "deviceName": device.deviceName ?? "",
            "mac": device.macAddress ?? "",
            "state": state.rawValue
        ]
        switch state {
        case .success:
            stateDict["stateDescription"] = "success"
            self.addDeviceToSyncList(device)
            self.startDataSync()
        case .failure:
            stateDict["stateDescription"] = "failure"
        case .unknown:
            stateDict["stateDescription"] = "unknown"
        @unknown default:
            stateDict["stateDescription"] = "unknown"
        }
        sendToFlutter(method: "pairingStateChanged", data: stateDict)
    }

    func bleDevice(_ device: LSDeviceInfo!, didPairMessageUpdate pairMsg: LSDevicePairMsg!) {
        print("DEBUG: Pair message update for device \(device.deviceName ?? "Unknown"): \(pairMsg.cmd.rawValue)")
        DispatchQueue.main.async {
            var messageDict: [String: Any] = [
                "deviceMac": device.macAddress ?? "",
                "command": pairMsg.cmd.rawValue
            ]

            switch pairMsg.cmd {
            case .randomCode:
                messageDict["type"] = "randomCodeRequest"
                messageDict["code"] = pairMsg.data ?? ""
            case .pairConfirm:
                messageDict["type"] = "userInfoRequired"
            case .pairRequest:
                messageDict["type"] = "pairRequest"
            default: break
            }

            self.sendToFlutter(method: "pairMessageUpdate", data: messageDict)
        }
    }
}

// MARK: - Data Delegate
extension BluetoothService: LSDeviceDataDelegate {
    func bleDevice(_ device: LSDeviceInfo!, didConnectStateChanged state: LSConnectState) {
        print("DEBUG: Connection state changed for device \(device.deviceName ?? "Unknown"): \(state.rawValue)")
        let stateDescription: String
        switch state {
        case .connecting: stateDescription = "connecting"
        case .connected: stateDescription = "connected"
        case .disconnect: stateDescription = "disconnected"
        case .failure: stateDescription = "failed"
        case .timeout: stateDescription = "timeout"
        case .success: stateDescription = "success"
        @unknown default: stateDescription = "unknown"
        }

        sendToFlutter(method: "connectionStateChanged", data: [
            "deviceName": device.deviceName ?? "",
            "mac": device.macAddress ?? "",
            "state": state.rawValue,
            "stateDescription": stateDescription
        ])
    }

    func bleDevice(_ device: LSDeviceInfo!, didDataUpdateForBloodPressureMonitor obj: LSBloodPressure!) {
        print("DEBUG: Blood pressure data update for device \(device.deviceName ?? "Unknown")")
        guard let obj = obj else { return }
        sendToFlutter(method: "bpData", data: [
            "systolic": obj.systolic,
            "diastolic": obj.diastolic,
            "pulse": obj.pluseRate,
            "timestamp": obj.utc
        ])
    }


    
    func bleDevice(_ device: LSDeviceInfo!, didDataUpdateForPulseOximeter data: LSDeviceData!) {
       
        let setting : LSPulseWaveSetting = LSPulseWaveSetting();
        setting.enable = true;
        manager?.pushSyncSetting(setting, forDevice: device, forResponse: { (state, error, data) in
            print("State: \(state), Error: \(String(describing: error)), Data: \(String(describing: data))")
        })
        
        if let bloodOxygen = data as? LSBloodOxygen {
            // Handle SpO2 Data
            let spo2 = bloodOxygen.value
            let pulseRate = bloodOxygen.pulseRate
            let pi = bloodOxygen.pi
            let measureTime = bloodOxygen.measureTime ?? "Unknown"
            
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
            let timestamp = formatter.date(from: measureTime)?.timeIntervalSince1970 ?? 0
            
            sendToFlutter(method: "pulseOxData", data: [
                "spo2": spo2,
                "pulseRate": pulseRate,
                "pi": pi,
                "timestamp": timestamp
            ])
        } else if let pulseWave = data as? LSPulseWave {
            let waveform = pulseWave.items?.compactMap { $0.intValue } ?? []
            let timestamp = pulseWave.utc
            let flag = pulseWave.flag
            let state = pulseWave.state
            let frequency = pulseWave.frequency
            let index = pulseWave.index
            
            sendToFlutter(method: "pulseWaveData", data: [
                "waveform": waveform,
                "timestamp": timestamp,
                "flag": flag,
                "state": state,
                "frequency": frequency,
                "index": index
            ])
        }
else {
            print("DEBUG: Unknown pulse oximeter data type")
        }
    }



    func bleDevice(_ device: LSDeviceInfo!, didDataUpdateForBloodGlucose obj: BGDataSummary!) {
        print("DEBUG: Blood glucose data update for device \(device.deviceName ?? "Unknown")")
        guard let data = obj, let firstItem = data.items?.first else { return }
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        let timestamp = formatter.date(from: data.measureTime ?? "")?.timeIntervalSince1970 ?? 0

        sendToFlutter(method: "glucoseData", data: [
            "value": firstItem.value,
            "timestamp": timestamp,
            "unit": firstItem.unit ?? "mmol/L"
        ])
    }
    
    
}
