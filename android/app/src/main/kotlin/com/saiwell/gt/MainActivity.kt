package com.saiwell.gt

import android.media.MediaActionSound
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity: FlutterActivity() {
    private val SOUND_CHANNEL = "com.telehealth.sound/channel"
    private var mediaActionSound: MediaActionSound? = null

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        // Initialize MediaActionSound for camera shutter
        mediaActionSound = MediaActionSound()
        mediaActionSound?.load(MediaActionSound.SHUTTER_CLICK)

        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, SOUND_CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "playShutterSound" -> {
                    try {
                        mediaActionSound?.play(MediaActionSound.SHUTTER_CLICK)
                        result.success(null)
                    } catch (e: Exception) {
                        result.error("SOUND_ERROR", "Failed to play shutter sound: ${e.message}", null)
                    }
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        mediaActionSound?.release()
        mediaActionSound = null
    }
}
