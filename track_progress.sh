#!/bin/bash

TOTAL_FILES=$(wc -l < /tmp/dsym_files.txt)
CURRENT_PID=$(ps aux | grep upload_dsyms.sh | grep -v grep | awk '{print $2}')

if [ -z "$CURRENT_PID" ]; then
  echo "Upload process is not running or has completed."
  exit 0
fi

# Create a temporary file to count completed uploads
touch /tmp/completed_count.txt

# Count the number of "Successfully uploaded" messages in the process output
COMPLETED_UPLOADS=$(ps -p $CURRENT_PID -o command= | wc -l)

# If we couldn't get the count directly, estimate based on time
if [ "$COMPLETED_UPLOADS" -eq 0 ]; then
  # Get the process start time
  START_TIME=$(ps -p $CURRENT_PID -o lstart= | date -j -f "%a %b %d %T %Y" "$(cat -)" "+%s")
  CURRENT_TIME=$(date +%s)
  ELAPSED_TIME=$((CURRENT_TIME - START_TIME))
  
  # Estimate ~45 seconds per file
  COMPLETED_UPLOADS=$((ELAPSED_TIME / 45))
  
  # Make sure we don't exceed the total
  if [ "$COMPLETED_UPLOADS" -gt "$TOTAL_FILES" ]; then
    COMPLETED_UPLOADS=$TOTAL_FILES
  fi
fi

PERCENT_COMPLETE=$(echo "scale=2; ($COMPLETED_UPLOADS / $TOTAL_FILES) * 100" | bc)

echo "Progress: $COMPLETED_UPLOADS out of $TOTAL_FILES files uploaded ($PERCENT_COMPLETE% complete)"
echo "Process has been running for $(ps -p $CURRENT_PID -o etime= | tr -d ' ') (hh:mm:ss)"
echo "Estimated time remaining: ~$(echo "scale=0; (($TOTAL_FILES - $COMPLETED_UPLOADS) * 45) / 60" | bc) minutes" 