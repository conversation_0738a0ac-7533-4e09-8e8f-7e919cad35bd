import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:get/get.dart';
import 'package:googleapis/storage/v1.dart';
import 'package:googleapis_auth/auth_io.dart';
import 'package:gt_plus/global_controller.dart';
import 'package:gt_plus/modules/BleFlutter/ble_flutter_controller.dart';
import 'package:gt_plus/modules/meta/view/meta_view.dart';
import 'package:gt_plus/modules/meta/controller/meta_controller.dart';
import 'package:gt_plus/modules/ppg/view/ppg_reading_view.dart';
import 'package:gt_plus/services/api_service.dart';
import 'package:gt_plus/services/prefs_service.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../../../models/clinic_details_model.dart';
import '../../../utils/reusableWidgets/resusable_snackbar.dart';

class PpgController extends GetxController {
  final ApiService _apiService = ApiService();
  final PrefsService _prefsService = PrefsService();
  final BleFlutterController bleFlutterController =
      Get.find<BleFlutterController>();
  final GlobalController globalController = Get.find<GlobalController>();

  Rx<DeviceStatus> deviceStatus = DeviceStatus.disconnected.obs;
  RxBool isLoading = false.obs;

  void checkSavedDevice() async {
    deviceStatus.value = DeviceStatus.connecting;
    String savedId = await _prefsService.getPpgRemoteId();
    if (savedId.isNotEmpty) {
      BluetoothDevice device =
          BluetoothDevice(remoteId: DeviceIdentifier(savedId));
      bool isConnected = await bleFlutterController.connect(device);
      if (!isConnected) {
        deviceStatus.value = DeviceStatus.disconnected;
        bleFlutterController.showDeviceSelectionPopup(BleDevice.ppg);
      }
    } else {
      deviceStatus.value = DeviceStatus.disconnected;
      bleFlutterController.showDeviceSelectionPopup(BleDevice.ppg);
    }
    _setupBleListeners();
  }

  void _setupBleListeners() {
    ever(bleFlutterController.connectedDevices,
        (Map<BleDevice, BluetoothDevice?> devices) {
      if (devices[BleDevice.ppg] != null) {
        deviceStatus.value = DeviceStatus.connected;
      } else {
        deviceStatus.value = DeviceStatus.disconnected;
        bleFlutterController.showDeviceSelectionPopup(BleDevice.ppg);
      }
    });
  }

  Future<String?> onMonitoringSubmitTap(BuildContext context, {bool shouldNavigateToMeta = true}) async {
    if (!_validateInputs()) {
      return null;
    }

    return await postPpg(
      spo2: bleFlutterController.spo2.value,
      heartRate: bleFlutterController.heartRate.value,
      context: context,
      shouldNavigateToMeta: shouldNavigateToMeta,
    );
  }

  bool _validateInputs() {
    final int spo2 = bleFlutterController.spo2.value;
    final int heartRate = bleFlutterController.heartRate.value;

    if (spo2 < 70 || spo2 > 100) {
      reusableSnackBar(
          message: "Invalid SpO2 value.should be between 70 and 100.");
      return false;
    }

    if (heartRate < 40 || heartRate > 180) {
      reusableSnackBar(
          message: "Invalid heart rate. should be between 40 and 180 bpm.");
      return false;
    }

    return true;
  }

  Future<String?> postPpg({
    required int spo2,
    required int heartRate,
    required BuildContext context,
    bool shouldNavigateToMeta = true,
  }) async {
    try {
      isLoading.value = true;
      String path = await _getFormattedFileName(suffix: "ppgWave");
      ClinicDetailsModel? clinicDetailsModel =
          await _prefsService.getClinicDetails();
      String bucketName = (clinicDetailsModel?.clinicName ?? "").toLowerCase();
      bool isCsvSubmitted = await uploadPPGDataToGCS(
        context: context,
        bucketName: bucketName,
        path: path,
      );
      if (isCsvSubmitted) {
        bool response = await _apiService.postPpg(
            spo2: spo2, heartRate: heartRate, path: "$bucketName/$path");
        if (response) {
          bleFlutterController.resetPpgTimer();

          // Update PPG completion status in meta controller
          try {
            final metaController = Get.find<MetaController>();
            metaController.updateModuleStatus(PpgReadingView.routeName, true);
            debugPrint("PPG completion status updated in meta controller");
          } catch (e) {
            debugPrint("Error updating PPG completion status: $e");
          }

          // Navigate to meta screen only if user was on PPG screen
          if (shouldNavigateToMeta) {
            Get.offAllNamed(MetaView.routeName);
            debugPrint("Navigated to meta screen after PPG auto-submit");
          } else {
            debugPrint("PPG auto-submitted successfully, staying on current screen");
          }

          return "$bucketName/$path";
        } else {
          reusableSnackBar(message: "Something went wrong...");
          return null;
        }
      } else {
        reusableSnackBar(message: "Something went wrong...");
        return null;
      }
    } catch (e) {
      debugPrint("Error: $e");
      reusableSnackBar(message: "Something went wrong...");
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  Future<bool> uploadPPGDataToGCS({
    required String bucketName,
    required String path,
    required BuildContext context,
  }) async {
    // Check if in demo mode
    if (await _isDemoMode()) {
      debugPrint("Demo mode active: Skipping PPG data upload to GCS");
      return true;
    }

    if (bleFlutterController.ecgData.isEmpty) {
      debugPrint("No ECG data available to upload");
      return false;
    }

    final csvData = _convertEcgToCsv();
    return await uploadCsvToGCS(
      csvData: csvData,
      bucketName: bucketName,
      gcsPath: path,
      context: context,
    );
  }

  Future<String> _getFormattedFileName({
    required String suffix,
  }) async {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    String identifier = await _prefsService.getIdentifier();
    final platform = Theme.of(Get.context!).platform == TargetPlatform.iOS
        ? 'ios'
        : 'android';
    final packageInfo = await PackageInfo.fromPlatform();
    final appVersion = 'v${packageInfo.version}';
    return '$identifier/${timestamp}_${platform}_${appVersion}_$suffix.csv';
  }

  String _convertEcgToCsv() {
    final csvBuffer = StringBuffer();
    // Add CSV header
    csvBuffer.writeln('index,value');

    // Add data rows
    for (final dataPoint in bleFlutterController.ecgData) {
      csvBuffer.writeln('${dataPoint.time},${dataPoint.value}');
    }

    return csvBuffer.toString();
  }

  Future<bool> _isDemoMode() async {
    return await _prefsService.getDemoMode();
  }

  Future<bool> uploadCsvToGCS({
    required String csvData,
    required String bucketName,
    required String gcsPath,
    required BuildContext context,
  }) async {
    try {
      final credentials = await loadServiceAccountCredentials();
      final httpClient = await clientViaServiceAccount(
        credentials,
        [
          StorageApi.devstorageReadWriteScope,
          StorageApi.devstorageFullControlScope
        ],
      );

      final storage = StorageApi(httpClient);
      final encodedData = utf8.encode(csvData);
      final media = Media(
        Stream.value(encodedData),
        encodedData.length,
        contentType: 'text/csv',
      );

      final response = await storage.objects.insert(
        Object(name: gcsPath),
        bucketName,
        uploadMedia: media,
      );
      debugPrint(
          "Success uploading CSV to GCS to bucketName: $bucketName\ngcsPath: $gcsPath");
      return response.id != null;
    } catch (e) {
      debugPrint("Error uploading CSV to GCS: $e");
      return false;
    }
  }

  Future<ServiceAccountCredentials> loadServiceAccountCredentials() async {
    String jsonContent =
        await rootBundle.loadString('assets/gCloud/credentials.json');
    final Map<String, dynamic> jsonMap = jsonDecode(jsonContent);
    return ServiceAccountCredentials.fromJson(jsonMap);
  }
}
