// import 'dart:async';
// import 'package:get/get.dart';
// import 'package:gt_plus/modules/bloodPressure/view/blood_pressure_reading_view.dart';
//
// import 'package:gt_plus/utils/reusableWidgets/resusable_snackbar.dart';
// import '../../../bluetooth_service.dart';
// import '../view/ppg_waveform.dart';
//
// class BleController extends GetxController {
//   final BluetoothService _bluetoothService = BluetoothService();
//
//   // Observable variables
//   final RxList<Map<String, dynamic>> devices = <Map<String, dynamic>>[].obs;
//   final RxBool isScanning = false.obs;
//   final RxInt bluetoothState = 1.obs; // 0 = unknown, 5 = powered on
//   final RxString statusMessage = "Ready to scan".obs;
//
//   // Device detail observables
//   final Rx<Map<String, dynamic>> selectedPpgDevice =
//       Rx<Map<String, dynamic>>({});
//   final Rx<Map<String, dynamic>> selectedBpDevice =
//       Rx<Map<String, dynamic>>({});
//   final RxString connectionState = "Disconnected".obs;
//   final RxString pairingState = "Not paired".obs;
//   final RxBool isPaired = false.obs;
//   final RxBool isConnecting = false.obs;
//
//   // Device type tracking
//   final RxBool isPpgDevice = true.obs; // Default to PPG device
//
//   // Data readings
//   final RxList<Map<String, dynamic>> bpReadings = <Map<String, dynamic>>[].obs;
//   final RxList<Map<String, dynamic>> pulseOxReadings =
//       <Map<String, dynamic>>[].obs;
//   final RxList<Map<String, dynamic>> pulseWaveReadings =
//       <Map<String, dynamic>>[].obs;
//
//   // Latest readings
//   Rx<PulseOxModel> lastPulse = PulseOxModel(spo2: "0", pulseRate: "0").obs;
//   Rx<BpModel> lastBp =
//       BpModel(systolic: "0", diastolic: "0", pulseRate: "0").obs;
//
//   final RxList<LiveData> ecgData = <LiveData>[].obs;
//   int timeCounter = 0;
//   final int maxDataPoints = 500;
//
//   // Stream subscriptions
//   late StreamSubscription _deviceFoundSub;
//   late StreamSubscription _bluetoothStatusSub;
//   late StreamSubscription _connectionSub;
//   late StreamSubscription _pairingSub;
//   late StreamSubscription _bpDataSub;
//   late StreamSubscription _pulseOxSub;
//   late StreamSubscription _pulseWaveSub;
//
//   @override
//   void onInit() {
//     super.onInit();
//     initializeBluetoothService();
//     //  _loadSavedDevices();
//   }
//
//   // Future<void> _loadSavedDevices() async {
//   //   final ppgDevice = await _prefsService.getPpgDevice();
//   //   if (ppgDevice.isNotEmpty) {
//   //     selectedPpgDevice.value = ppgDevice;
//   //   }
//   //
//   //   final bpDevice = await _prefsService.getBpDevice();
//   //   if (bpDevice.isNotEmpty) {
//   //     selectedBpDevice.value = bpDevice;
//   //   }
//   // }
//
//   Future<void> initializeBluetoothService() async {
//     try {
//       await _bluetoothService.initialize();
//       _setupListeners();
//       final int currentState =
//           await _bluetoothService.getCurrentBluetoothState();
//       bluetoothState.value = currentState;
//     } catch (e) {
//       Get.snackbar('Error', 'Bluetooth initialization failed: $e');
//     }
//   }
//
//   void _setupListeners() {
//     // Device found listener
//     _deviceFoundSub = _bluetoothService.onDeviceFound.listen((device) {
//       // Check if device already exists to avoid duplicates
//       if (!devices.any((d) => d['mac'] == device['mac'])) {
//         devices.add(device);
//       }
//     });
//
//     // Bluetooth status listener
//     _bluetoothStatusSub =
//         _bluetoothService.onBluetoothStatusChanged.listen((state) {
//       bluetoothState.value = state;
//       if (state != 5) {
//         // 5 = powered on
//         isScanning.value = false;
//         statusMessage.value = "Bluetooth is not available";
//       } else {
//         statusMessage.value = "Ready to scan";
//       }
//     });
//
//     // Connection state changes
//     _connectionSub = _bluetoothService.onConnectionStateChanged.listen((state) {
//       final String deviceMac = state['mac'] ?? '';
//       final bool isPpgDeviceConnected =
//           deviceMac == selectedPpgDevice.value['mac'];
//       final bool isBpDeviceConnected =
//           deviceMac == selectedBpDevice.value['mac'];
//
//       if (isPpgDeviceConnected || isBpDeviceConnected) {
//         connectionState.value = state['stateDescription'] ?? "Unknown";
//
//         if (state['stateDescription'] == 'connected') {
//           Future.delayed(const Duration(seconds: 1), () {
//             _bluetoothService.startDataSync(
//                 deviceType: isPpgDeviceConnected ? 'ppg' : 'bp');
//           });
//         }
//       }
//     });
//
//     // Pairing state changes
//     _pairingSub = _bluetoothService.onPairingStateChanged.listen((state) {
//       final String deviceMac = state['mac'] ?? '';
//       final bool isPpgDevicePairing =
//           deviceMac == selectedPpgDevice.value['mac'];
//       final bool isBpDevicePairing = deviceMac == selectedBpDevice.value['mac'];
//
//       if (isPpgDevicePairing || isBpDevicePairing) {
//         pairingState.value = state['stateDescription'] ?? "Unknown";
//         isPaired.value = pairingState.value == "success";
//
//         if (isPaired.value) {
//           // Auto-sync after successful pairing
//           syncData(deviceType: isPpgDevicePairing ? 'ppg' : 'bp');
//         }
//       }
//     });
//
//     // Blood pressure data received
//     _bpDataSub = _bluetoothService.onBPDataReceived.listen((data) {
//       // Add timestamp if not present
//       if (!data.containsKey('timestamp')) {
//         data['timestamp'] = DateTime.now().millisecondsSinceEpoch / 1000;
//       }
//       bpReadings.insert(0, data);
//
//       // Update latest BP reading
//       if (data.containsKey('systolic') &&
//           data.containsKey('diastolic') &&
//           data.containsKey('pulse')) {
//         lastBp.value = BpModel(
//             systolic: data["systolic"].toString(),
//             diastolic: data["diastolic"].toString(),
//             pulseRate: data["pulse"].toString());
//       }
//     });
//
//     // Pulse oximeter data
//     _pulseOxSub = _bluetoothService.onPulseOxData.listen((data) {
//       if (!data.containsKey('timestamp')) {
//         data['timestamp'] = DateTime.now().millisecondsSinceEpoch / 1000;
//       }
//       pulseOxReadings.insert(0, data);
//       lastPulse.value = PulseOxModel(
//           spo2: data["spo2"].toString(),
//           pulseRate: data["pulseRate"].toString());
//     });
//
//     // Waveform data
//     _pulseWaveSub = _bluetoothService.onPulseWaveData.listen((data) {
//       final List<int> newData = List<int>.from(data['waveform'] ?? []);
//
//       if (newData.isNotEmpty) {
//         final int firstValue = newData.first;
//         if (ecgData.length >= maxDataPoints) {
//           ecgData.removeAt(0);
//         }
//         ecgData.add(LiveData(timeCounter++, firstValue.toDouble()));
//         ecgData.refresh();
//       }
//     });
//   }
//
//   void startScan() {
//     if (bluetoothState.value != 5) {
//       Get.snackbar('Error', 'Bluetooth is not available');
//       return;
//     }
//
//     isScanning.value = true;
//     statusMessage.value = "Scanning for devices...";
//     devices.clear();
//
//     _bluetoothService.startScan();
//   }
//
//   Future<void> stopScan() async {
//     await _bluetoothService.stopScan();
//     isScanning.value = false;
//     statusMessage.value = "Scan completed. Found ${devices.length} devices.";
//   }
//
//   Future<void> selectDevice(
//     Map<String, dynamic> device,
//     bool showShowSnackBar,
//     String deviceType,
//   ) async {
//     if (isScanning.value) {
//       await stopScan();
//     }
//     bool isPaired = await _bluetoothService.pairDevice(device);
//     if (isPaired) {
//       if (deviceType == 'ppg') {
//         selectedPpgDevice.value = device;
//         //   _prefsService.setPpgDevice(device);
//         isPpgDevice.value = true;
//
//         // if (Get.previousRoute == PpgInstructionView.routeName) {
//         //   Get.offNamed(PpgReadingView.routeName);
//         // }
//       } else if (deviceType == 'bp') {
//         selectedBpDevice.value = device;
//         // _prefsService.setBpDevice(device);
//         isPpgDevice.value = false;
//         Get.offNamed(BloodPressureReadingView.routeName);
//       }
//     } else {
//       if (showShowSnackBar) {
//         reusableSnackBar(message: "Cannot pair device");
//       }
//     }
//   }
//
//   Future<bool> pairDevice({String deviceType = 'ppg'}) async {
//     pairingState.value = "Pairing...";
//
//     if (deviceType == 'ppg') {
//       return await _bluetoothService.pairDevice(selectedPpgDevice.value['mac']);
//     } else {
//       return await _bluetoothService.pairDevice(selectedBpDevice.value['mac']);
//     }
//   }
//
//   void syncData({String deviceType = 'ppg'}) {
//     isConnecting.value = true;
//     connectionState.value = "Connecting...";
//
//     // Start data sync with device
//     _bluetoothService.startDataSync(deviceType: deviceType);
//   }
//
//   // Helper method to check if a device is a BP device
//   bool isBpDevice(Map<String, dynamic> device) {
//     // Logic to determine if a device is a BP device
//     // This could be based on device name, services, etc.
//     final String deviceName = device['name'] ?? '';
//     return deviceName.toLowerCase().contains('bp') ||
//         deviceName.toLowerCase().contains('blood') ||
//         deviceName.toLowerCase().contains('pressure');
//   }
//
//   @override
//   void onClose() {
//     // Cancel all subscriptions
//     _deviceFoundSub.cancel();
//     _bluetoothStatusSub.cancel();
//     _connectionSub.cancel();
//     _pairingSub.cancel();
//     _bpDataSub.cancel();
//     _pulseOxSub.cancel();
//     _pulseWaveSub.cancel();
//
//     // Stop scanning if active
//     if (isScanning.value) {
//       _bluetoothService.stopScan();
//     }
//
//     super.onClose();
//   }
// }
//
// class PulseOxModel {
//   final String spo2;
//   final String pulseRate;
//
//   PulseOxModel({
//     required this.spo2,
//     required this.pulseRate,
//   });
// }
//
// class BpModel {
//   final String systolic;
//   final String diastolic;
//   final String pulseRate;
//
//   BpModel({
//     required this.systolic,
//     required this.diastolic,
//     required this.pulseRate,
//   });
// }
