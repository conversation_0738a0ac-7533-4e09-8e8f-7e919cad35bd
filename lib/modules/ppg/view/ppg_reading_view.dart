import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gt_plus/modules/ppg/controller/ppg_controller.dart';
import '../../../utils/appConst/app_colors.dart';
import '../../../utils/appConst/app_images.dart';
import '../../../utils/reusableWidgets/reusable_app_bar.dart';
import '../../../utils/reusableWidgets/reusable_data_reading_container.dart';
import '../../../utils/reusableWidgets/reusable_footer.dart';

class PpgReadingView extends StatefulWidget {
  const PpgReadingView({super.key});
  static const String routeName = "/PpgReadingView";

  @override
  State<PpgReadingView> createState() => _PpgReadingViewState();
}

class _PpgReadingViewState extends State<PpgReadingView> {
  PpgController controller = Get.find<PpgController>();

  @override
  void initState() {
    super.initState();
    controller.checkSavedDevice();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: reusableAppBar(
        context: context,
      ),
      bottomNavigationBar: const ReusableFooter(),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: context.width * .04),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: context.height * .04,
              ),
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: AppColors.lightTeal,
                  borderRadius: BorderRadius.circular(5),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      flex: 2,
                      child: Padding(
                        padding: EdgeInsets.only(
                            left: context.width * .02, top: 14, bottom: 14),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              "PPG Monitoring",
                              style: TextStyle(
                                color: AppColors.white,
                                fontSize: 32,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(
                              height: 4,
                            ),
                            ...?controller.globalController.instructions.ppg
                                ?.asMap()
                                .entries
                                .map((entry) {
                              final index = entry.key;
                              final point = entry.value;
                              return Padding(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 4.0),
                                child: Text(
                                  '${index + 1}. $point',
                                  style: const TextStyle(
                                    color: Colors.white,
                                  ),
                                ),
                              );
                            }),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(
                      width: 32,
                    ),
                    // Expanded(
                    //   child: Image.asset(
                    //     AppImages.imgPPG,
                    //   ),
                    // ),
                  ],
                ),
              ),
              SizedBox(
                height: context.height * .03,
              ),
              Row(
                children: [
                  const Text(
                    "PPG",
                    style: TextStyle(
                      color: AppColors.charcoalBlue,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(
                    width: 16,
                  ),
                  Obx(
                    () => Text(
                      controller.deviceStatus.value.text,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: controller.deviceStatus.value.color,
                      ),
                    ),
                  )
                ],
              ),
              SizedBox(
                height: context.height * .02,
              ),
              Obx(
                () => Row(
                  children: [
                    ReusableDataReadingContainer(
                      dataType: "SpO2",
                      dataValue:
                          controller.bleFlutterController.spo2.value.toString(),
                      dataUnit: '%',
                      icon: AppImages.icSpo2,
                    ),
                    const SizedBox(
                      width: 36,
                    ),
                    ReusableDataReadingContainer(
                      dataType: "Pulse Rate",
                      dataValue: controller.bleFlutterController.heartRate.value
                          .toString(),
                      dataUnit: 'bpm',
                      icon: AppImages.icPulse,
                    ),
                  ],
                ),
              ),
              const SizedBox(
                height: 28,
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Submit button removed - auto-submit when timer reaches 00:00
                  Obx(
                    () => Visibility(
                      visible: controller.bleFlutterController
                              .canShowSubmitButtonForPPG.value &&
                          controller.isLoading.value,
                      child: const Padding(
                        padding: EdgeInsets.only(top: 12),
                        child: Row(
                          children: [
                            SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                    AppColors.darkTeal),
                              ),
                            ),
                            SizedBox(width: 12),
                            Text(
                              'Auto-submitting PPG data...',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                color: AppColors.darkTeal,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  Obx(
                    () => Visibility(
                      visible: !controller
                          .bleFlutterController.canShowSubmitButtonForPPG.value,
                      child: Padding(
                        padding: const EdgeInsets.only(top: 12),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.baseline,
                              textBaseline: TextBaseline.alphabetic,
                              children: [
                                const Text(
                                  'Time left: ',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: AppColors.charcoalBlue,
                                  ),
                                ),
                                Text(
                                  _formatRemainingTime(controller
                                      .bleFlutterController
                                      .remainingSeconds
                                      .value),
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w600,
                                    color: AppColors.darkTeal,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            TextButton(
                              onPressed: () {
                                Get.back();
                              },
                              style: TextButton.styleFrom(
                                minimumSize: Size.zero,
                                padding: const EdgeInsets.only(top: 8,bottom: 8),
                                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                              ),
                              child: const Text(
                                'You can proceed to other screens while ppg is being recorded !!',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.orange,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatRemainingTime(int seconds) {
    int minutes = seconds ~/ 60;
    int remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }
}
