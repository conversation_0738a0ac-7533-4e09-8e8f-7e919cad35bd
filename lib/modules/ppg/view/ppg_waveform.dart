import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gt_plus/modules/BleFlutter/ble_flutter_controller.dart';
import 'package:gt_plus/utils/appConst/app_colors.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

class PPGWaveform extends StatelessWidget {
  const PPGWaveform({super.key});

  @override
  Widget build(BuildContext context) {
    final bleFlutterController = Get.find<BleFlutterController>();
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Obx(() {
        final data = bleFlutterController.ecgData.value;
        debugPrint(" PPG data lenght ${data.length}");
        final displayData =
        data.length > 500 ? data.sublist(data.length - 500) : data;
        return SfCartesian<PERSON>hart(
          plotAreaBorderWidth: 0,
          primaryXAxis: const NumericAxis(
            isVisible: false,
            autoScrollingMode: AutoScrollingMode.end,
          ),
          primaryYAxis: const NumericAxis(
            isVisible: false,
            minimum: 0,
            maximum: 135,
          ),
          series: <SplineSeries<LiveData, int>>[
            SplineSeries<LiveData, int>(
              dataSource: displayData,
              color: AppColors.skyBlue,
              width: 2,
              splineType: SplineType.cardinal,
              cardinalSplineTension: 0.5,
              xValueMapper: (LiveData data, _) => data.time,
              yValueMapper: (LiveData data, _) => data.value,
              animationDuration: 0,
            )
          ],
        );
      }),
    );
  }
}

class LiveData {
  LiveData(this.time, this.value);
  final int time;
  final num value;
}