// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:gt_plus/modules/ppg/controller/ppg_bluetooth_controller.dart';
// import 'package:gt_plus/modules/ppg/view/ppg_instruction_view.dart';
// import 'package:gt_plus/utils/reusableWidgets/reusable_app_bar.dart';
// import 'package:gt_plus/utils/reusableWidgets/reusable_button.dart';
//
// import '../../../utils/appConst/app_colors.dart';
//
// class BleScanView extends GetView<BleController> {
//   const BleScanView({super.key});
//   static const String routeName = "/BleScanView";
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: reusableAppBar(context: context),
//       body: Column(
//         children: [
//           SizedBox(
//             height: context.height * .05,
//           ),
//           Obx(
//             () => ReusableButton(
//                 width: context.width * .25,
//                 height: 50,
//                 title: controller.isScanning.value ? 'Stop Scan' : 'Start Scan',
//                 onTap: () {
//                   controller.isScanning.value
//                       ? controller.stopScan()
//                       : controller.startScan();
//                 }),
//           ),
//           SizedBox(
//             height: context.height * .05,
//           ),
//           Obx(() => Container(
//                 padding:
//                     const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
//                 color: Colors.grey[200],
//                 width: double.infinity,
//                 child: Text(controller.statusMessage.value),
//               )),
//           Expanded(
//             child: Obx(() => controller.devices.isEmpty
//                 ? Center(
//                     child: Text(
//                       controller.isScanning.value
//                           ? 'Searching for devices...'
//                           : 'No devices found. Tap Start Scan to begin.',
//                       style: const TextStyle(color: Colors.grey),
//                     ),
//                   )
//                 : ListView.builder(
//                     itemCount: controller.devices.length,
//                     itemBuilder: (context, index) {
//                       final device = controller.devices[index];
//                       return ListTile(
//                         leading: const Icon(
//                           Icons.medical_services,
//                           color: AppColors.orange,
//                         ),
//                         title: Text('MAC: ${device['mac']}'),
//                         trailing: ReusableButton(
//                           height: 38,
//                           width: 115,
//                           color: AppColors.primaryTeal,
//                           borderColor: AppColors.primaryTeal,
//                           fontSize: 18,
//                           title: 'Connect',
//                           onTap: () {
//                             // if (Get.previousRoute ==
//                             //     PpgInstructionView.routeName) {
//                             //   controller.selectDevice(device, true, 'ppg');
//                             // } else {
//                             controller.selectDevice(device, true, 'bp');
//                             //   }
//                           },
//                         ),
//                       );
//                     },
//                   )),
//           ),
//         ],
//       ),
//     );
//   }
// }
