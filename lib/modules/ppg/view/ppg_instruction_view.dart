// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:gt_plus/modules/ppg/controller/ppg_controller.dart';
// import 'package:gt_plus/utils/appConst/app_colors.dart';
// import 'package:gt_plus/utils/appConst/app_images.dart';
// import 'package:gt_plus/utils/reusableWidgets/reusable_button.dart';
//
// import '../../../utils/reusableWidgets/reusable_app_bar.dart';
// import '../../../utils/reusableWidgets/reusable_footer.dart';
//
// class PpgInstructionView extends GetView<PpgController> {
//   const PpgInstructionView({super.key});
//   static const String routeName = "/PpgInstructionView";
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: reusableAppBar(
//         context: context,
//       ),
//       body: Column(
//         children: [
//           Expanded(
//             child: SingleChildScrollView(
//               child: Padding(
//                 padding: EdgeInsets.symmetric(horizontal: context.width * .04),
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     SizedBox(
//                       height: context.height * .04,
//                     ),
//                     Container(
//                       height: context.height * .2,
//                       width: double.infinity,
//                       decoration: BoxDecoration(
//                         color: AppColors.lightTeal,
//                         borderRadius: BorderRadius.circular(5),
//                       ),
//                       child: Row(
//                         crossAxisAlignment: CrossAxisAlignment.center,
//                         children: [
//                           Padding(
//                             padding: EdgeInsets.only(left: context.width * .02),
//                             child: const Column(
//                               mainAxisAlignment: MainAxisAlignment.center,
//                               crossAxisAlignment: CrossAxisAlignment.start,
//                               children: [
//                                 Text(
//                                   "PPG Monitoring",
//                                   style: TextStyle(
//                                     color: AppColors.white,
//                                     fontSize: 32,
//                                     fontWeight: FontWeight.bold,
//                                   ),
//                                 ),
//                                 SizedBox(
//                                   height: 4,
//                                 ),
//                                 Text(
//                                   "Step-by-step guide to setting up and recording PPG.\nEnsure accuracy for better health insights.",
//                                   style: TextStyle(
//                                     color: AppColors.white,
//                                     fontSize: 18,
//                                   ),
//                                 ),
//                               ],
//                             ),
//                           ),
//                           const Spacer(),
//                           Image.asset(
//                             AppImages.imgPPG,
//                           ),
//                         ],
//                       ),
//                     ),
//                     SizedBox(
//                       height: context.height * .03,
//                     ),
//                     const Text(
//                       "Please follow instruction:",
//                       style: TextStyle(
//                         color: AppColors.charcoalBlue,
//                         fontSize: 24,
//                         fontWeight: FontWeight.bold,
//                       ),
//                     ),
//                     SizedBox(
//                       height: context.height * .02,
//                     ),
//                     ListView.separated(
//                       itemCount: controller.instructions.length,
//                       physics: const NeverScrollableScrollPhysics(),
//                       shrinkWrap: true,
//                       itemBuilder: (context, index) {
//                         return Row(
//                           children: [
//                             Text(
//                               "${index + 1}. ${controller.instructions[index].title}: ",
//                               style: const TextStyle(
//                                 color: AppColors.dimGray,
//                                 fontSize: 18,
//                                 fontWeight: FontWeight.w700,
//                               ),
//                             ),
//                             Text(
//                               controller.instructions[index].description,
//                               style: const TextStyle(
//                                 color: AppColors.smokeGray,
//                                 fontSize: 18,
//                               ),
//                             ),
//                           ],
//                         );
//                       },
//                       separatorBuilder: (BuildContext context, int index) {
//                         return const SizedBox(
//                           height: 8,
//                         );
//                       },
//                     ),
//                     const SizedBox(
//                       height: 32,
//                     ),
//                     ReusableButton(
//                       width: context.width * .25,
//                       title: 'Start process',
//                       onTap: () {
//                         controller.onStartProcessTap();
//                       },
//                     ),
//                   ],
//                 ),
//               ),
//             ),
//           ),
//           const Align(
//               alignment: Alignment.bottomCenter, child: ReusableFooter()),
//         ],
//       ),
//     );
//   }
// }
