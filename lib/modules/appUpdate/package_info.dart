import 'package:package_info_plus/package_info_plus.dart';

class PackageInfoSetup {
  static final PackageInfoSetup _singleton = PackageInfoSetup._internal();

  factory PackageInfoSetup() {
    return _singleton;
  }
  PackageInfoSetup._internal();
  late String version;
  late String packageName;
  late String appName;

  Future<void> initialize() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    version = packageInfo.version;
    packageName = packageInfo.packageName;
    appName = packageInfo.appName;
  }
}
