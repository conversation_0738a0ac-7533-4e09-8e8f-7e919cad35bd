import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:get/get.dart';
import 'package:gt_plus/modules/bloodPressure/view/blood_pressure_reading_view.dart';
import 'package:gt_plus/services/api_service.dart';
import 'package:gt_plus/services/prefs_service.dart';
import '../../../global_controller.dart';
import '../../../utils/reusableWidgets/resusable_snackbar.dart';
import '../../../utils/reusableWidgets/reusable_button.dart';
import '../../../utils/reusableWidgets/reusable_dialog.dart';
import '../../BleFlutter/ble_flutter_controller.dart';
import '../../meta/view/meta_view.dart';

class BloodPressureController extends GetxController {
  RxBool isLoading = false.obs;
  final ApiService _apiService = ApiService();
  final BleFlutterController bleFlutterController =
      Get.find<BleFlutterController>();
  final PrefsService _prefsService = PrefsService();
  final GlobalController globalController = Get.find<GlobalController>();

  // Flag to track if this is the initial page load
  bool _isInitialLoad = true;

  Rx<DeviceStatus> deviceStatus = DeviceStatus.disconnected.obs;

  TextEditingController systolicBPController = TextEditingController();
  TextEditingController diastolicBPController = TextEditingController();

  @override
  void onInit() {
    super.onInit();
    _isInitialLoad = true;
  }

  @override
  void onReady() {
    _checkSavedDevice();
    super.onReady();
  }

  void _checkSavedDevice() async {
    deviceStatus.value = DeviceStatus.connecting;

    String savedId = await _prefsService.getBpRemoteId();
    if (savedId.isNotEmpty) {
      BluetoothDevice device =
          BluetoothDevice(remoteId: DeviceIdentifier(savedId));
      bool isConnected = await bleFlutterController.connect(device);
      if (!isConnected) {
        deviceStatus.value = DeviceStatus.disconnected;
        if (_isInitialLoad) {
          _showInstructionsDialog();
          _isInitialLoad = false;
        } else {
          bleFlutterController.showDeviceSelectionPopup(BleDevice.bp);
        }
      }
    } else {
      deviceStatus.value = DeviceStatus.disconnected;
      if (_isInitialLoad) {
        _showInstructionsDialog();
        _isInitialLoad = false;
      } else {
        bleFlutterController.showDeviceSelectionPopup(BleDevice.bp);
      }
    }
    _setupBleListeners();
  }

  void _showInstructionsDialog() {
    // Only show instruction popup if we're currently on the BP screen
    if (Get.currentRoute != BloodPressureReadingView.routeName) {
      return;
    }

    ReusableDialog.show(
      isDismissible: false,
      borderRadius: BorderRadius.circular(8),
      contentPadding: const EdgeInsets.symmetric(
        horizontal: 24,
        vertical: 36,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text(
            "Blood Pressure Instructions",
            style: TextStyle(
              fontWeight: FontWeight.w700,
              fontSize: 24,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ...?globalController.instructions.bp?.asMap().entries.map((entry) {
            final index = entry.key;
            final instruction = entry.value;
            return Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text("${index + 1}. ",
                      style: const TextStyle(fontWeight: FontWeight.bold)),
                  Expanded(child: Text(instruction)),
                ],
              ),
            );
          }),
          const SizedBox(height: 24),
          ReusableButton(
            title: "OK",
            onTap: () {
              ReusableDialog.close();
              bleFlutterController.showDeviceSelectionPopup(BleDevice.bp);
            },
          ),
        ],
      ),
    );
  }

  void _setupBleListeners() {
    ever(bleFlutterController.systolicBP, (value) {
      systolicBPController.text = value.toString();
    });

    ever(bleFlutterController.diastolicBP, (value) {
      diastolicBPController.text = value.toString();
    });

    ever(bleFlutterController.connectedDevices,
        (Map<BleDevice, BluetoothDevice?> devices) {
      if (devices[BleDevice.bp] != null) {
        deviceStatus.value = DeviceStatus.connected;
      } else {
        deviceStatus.value = DeviceStatus.disconnected;
      }

    });
  }

  void resetFrom() {
    systolicBPController.clear();
    diastolicBPController.clear();
    _checkSavedDevice();
  }

  // @override
  // void onClose() {
  //   systolicBPController.dispose();
  //   diastolicBPController.dispose();
  //   super.onClose();
  // }

  int? _parseIntValue(String value) {
    if (value.isEmpty) return null;
    try {
      return int.parse(value);
    } catch (e) {
      return null;
    }
  }

  Future<void> onSubmitClick() async {
    String systolicText = systolicBPController.text.trim();
    String diastolicText = diastolicBPController.text.trim();

    if (systolicText.isEmpty || diastolicText.isEmpty) {
      reusableSnackBar(
          message: "Please enter both Systolic and Diastolic values.");
      return;
    }

    int? systolicBP = _parseIntValue(systolicText);
    int? diastolicBP = _parseIntValue(diastolicText);

    if (systolicBP == null || diastolicBP == null) {
      reusableSnackBar(message: "Invalid values. Please enter numbers only.");
      return;
    }

    if (systolicBP < 50 ||
        systolicBP > 250 ||
        diastolicBP < 30 ||
        diastolicBP > 150) {
      reusableSnackBar(
          message:
              "Please enter realistic Blood Pressure values.\nSystolic: 50-250 mmHg, Diastolic: 30-150 mmHg.");
      return;
    }

    await postBloodPressure(systolic: systolicBP, diastolic: diastolicBP);
  }

  Future<void> postBloodPressure(
      {required int systolic, required int diastolic}) async {
    try {
      isLoading.value = true;
      bool response =
          await _apiService.postBp(systolic: systolic, diastolic: diastolic);
      if (response) {
        Get.offAllNamed(MetaView.routeName);
      } else {
        reusableSnackBar(message: "Something went wrong...");
      }
    } catch (e) {
      debugPrint("Error: $e");
      reusableSnackBar(message: "Something went wrong...");
    } finally {
      isLoading.value = false;
    }
  }
}
