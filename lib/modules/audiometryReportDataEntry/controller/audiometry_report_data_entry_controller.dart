import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gt_plus/models/audiometry_details_model.dart';
import 'package:intl/intl.dart';

import '../../../services/api_service.dart';
import '../../../utils/reusableWidgets/resusable_snackbar.dart';
import '../../../utils/validators/date_validator.dart';
import '../../meta/view/meta_view.dart';
import '../../meta/controller/meta_controller.dart';
import '../view/audiometry_report_data_entry_view.dart';

class AudiometryReportDataEntryController extends GetxController {
  // Left Ear Threshold Controllers
  final left250HzController = TextEditingController();
  final left500HzController = TextEditingController();
  final left1000HzController = TextEditingController();
  final left1500HzController = TextEditingController();
  final left2000HzController = TextEditingController();
  final left3000HzController = TextEditingController();
  final left4000HzController = TextEditingController();
  final left6000HzController = TextEditingController();
  final left8000HzController = TextEditingController();

  // Right Ear Threshold Controllers
  final right250HzController = TextEditingController();
  final right500HzController = TextEditingController();
  final right1000HzController = TextEditingController();
  final right1500HzController = TextEditingController();
  final right2000HzController = TextEditingController();
  final right3000HzController = TextEditingController();
  final right4000HzController = TextEditingController();
  final right6000HzController = TextEditingController();
  final right8000HzController = TextEditingController();

  // Word Recognition Score Controllers
  final leftWRSController = TextEditingController();
  final rightWRSController = TextEditingController();

  // Tympanometry Controllers
  final leftPeakPressureController = TextEditingController();
  final rightPeakPressureController = TextEditingController();

  //testDate
  final testDateController = TextEditingController();

  // Error messages for validation
  final left250HzError = ''.obs;
  final left500HzError = ''.obs;
  final left1000HzError = ''.obs;
  final left1500HzError = ''.obs;
  final left2000HzError = ''.obs;
  final left3000HzError = ''.obs;
  final left4000HzError = ''.obs;
  final left6000HzError = ''.obs;
  final left8000HzError = ''.obs;

  final right250HzError = ''.obs;
  final right500HzError = ''.obs;
  final right1000HzError = ''.obs;
  final right1500HzError = ''.obs;
  final right2000HzError = ''.obs;
  final right3000HzError = ''.obs;
  final right4000HzError = ''.obs;
  final right6000HzError = ''.obs;
  final right8000HzError = ''.obs;

  final leftWRSError = ''.obs;
  final rightWRSError = ''.obs;

  final leftPeakPressureError = ''.obs;
  final rightPeakPressureError = ''.obs;

  final testDateError = ''.obs;

  RxBool isLoading = false.obs;
  final ApiService _apiService = ApiService();

  // Read-only mode management
  final RxBool isReadOnly = false.obs;
  final RxBool isFormCompleted = false.obs;
  final RxBool isInitializing = true.obs;

  @override
  void onInit() {
    super.onInit();

    // Check completion status first, then fetch data
    _initializeForm();
  }

  Future<void> _initializeForm() async {
    try {
      // Check completion status immediately
      await _checkCompletionStatus();

      // Then fetch saved data
      await fetchSavedAudiometryData();
    } finally {
      // Mark initialization as complete
      isInitializing.value = false;
    }
  }

  // Method to enable read-only mode
  void enableReadOnlyMode() {
    debugPrint("Enabling read-only mode for audiometry form");
    isReadOnly.value = true;
    isFormCompleted.value = true;
  }

  // Method to enable edit mode
  void enableEditMode() {
    isReadOnly.value = false;
  }

  // Method to reset form
  void resetForm() {
    // Clear all controllers
    left250HzController.clear();
    left500HzController.clear();
    left1000HzController.clear();
    left1500HzController.clear();
    left2000HzController.clear();
    left3000HzController.clear();
    left4000HzController.clear();
    left6000HzController.clear();
    left8000HzController.clear();

    right250HzController.clear();
    right500HzController.clear();
    right1000HzController.clear();
    right1500HzController.clear();
    right2000HzController.clear();
    right3000HzController.clear();
    right4000HzController.clear();
    right6000HzController.clear();
    right8000HzController.clear();

    leftWRSController.clear();
    rightWRSController.clear();
    leftPeakPressureController.clear();
    rightPeakPressureController.clear();
    testDateController.clear();

    // Reset observables
    isReadOnly.value = false;
    isFormCompleted.value = false;

    // Clear all error messages
    left250HzError.value = '';
    left500HzError.value = '';
    left1000HzError.value = '';
    left1500HzError.value = '';
    left2000HzError.value = '';
    left3000HzError.value = '';
    left4000HzError.value = '';
    left6000HzError.value = '';
    left8000HzError.value = '';

    right250HzError.value = '';
    right500HzError.value = '';
    right1000HzError.value = '';
    right1500HzError.value = '';
    right2000HzError.value = '';
    right3000HzError.value = '';
    right4000HzError.value = '';
    right6000HzError.value = '';
    right8000HzError.value = '';

    leftWRSError.value = '';
    rightWRSError.value = '';
    leftPeakPressureError.value = '';
    rightPeakPressureError.value = '';
    testDateError.value = '';
  }

  @override
  void onClose() {
    left250HzController.dispose();
    left500HzController.dispose();
    left1000HzController.dispose();
    left1500HzController.dispose();
    left2000HzController.dispose();
    left3000HzController.dispose();
    left4000HzController.dispose();
    left6000HzController.dispose();
    left8000HzController.dispose();

    right250HzController.dispose();
    right500HzController.dispose();
    right1000HzController.dispose();
    right1500HzController.dispose();
    right2000HzController.dispose();
    right3000HzController.dispose();
    right4000HzController.dispose();
    right6000HzController.dispose();
    right8000HzController.dispose();

    leftWRSController.dispose();
    rightWRSController.dispose();

    leftPeakPressureController.dispose();
    rightPeakPressureController.dispose();

    testDateController.dispose();

    super.onClose();
  }

  // Validate hearing threshold values (can include negative sign and decimal point)
  bool _validateHearingThreshold(String value, RxString errorField, {bool isRequired = true}) {
    if (value.isEmpty) {
      if (isRequired) {
        errorField.value = 'This field is required';
        return false;
      } else {
        errorField.value = '';
        return true;
      }
    }

    // Regex to validate format: Optional negative sign, digits, optional decimal with digits
    // Total length must be 7 characters or less
    final regex = RegExp(r'^-?\d+(\.\d+)?$');
    if (!regex.hasMatch(value)) {
      errorField.value = 'Please enter a valid number';
      return false;
    }

    if (value.length > 7) {
      errorField.value = 'Value should not exceed 7 characters';
      return false;
    }

    try {
      double numValue = double.parse(value); // Ensure it's a valid number

      if (numValue > 1000) {
        errorField.value = 'Value should not be greater than 1000';
        return false;
      }

      errorField.value = '';
      return true;
    } catch (e) {
      errorField.value = 'Please enter a valid number';
      return false;
    }
  }

  // Validate WRS scores (0-100%)
  bool _validateWRS(String value, RxString errorField) {
    if (value.isEmpty) {
      return true;
    }

    try {
      double score = double.parse(value);
      if (score < 0 || score > 100) {
        errorField.value = 'Value must be between 0-100';
        return false;
      }
      errorField.value = '';
      return true;
    } catch (e) {
      errorField.value = 'Please enter a valid percentage';
      return false;
    }
  }

  bool _validatePeakPressure(String value, RxString errorField) {
    if (value.isEmpty) {
      return true;
    }

    // Allow optional '-' followed by digits with optional decimal part
    final regex = RegExp(r'^-?(\d{0,4}(\.\d*)?|\.\d+)$');
    if (!regex.hasMatch(value)) {
      errorField.value =
          'Please enter a valid number (up to 4 digits with optional decimals)';
      return false;
    }

    try {
      double.parse(value);
      errorField.value = '';
      return true;
    } catch (e) {
      errorField.value = 'Please enter a valid number';
      return false;
    }
  }

  bool _validateTestDate(String dobText) {
    return DateValidator.validateTestDate(dobText, testDateError);
  }

  // Validate all fields
  bool validateForm() {
    bool isValid = true;

    // Validate Left Ear Thresholds
    isValid =
        _validateHearingThreshold(left250HzController.text, left250HzError) &&
            isValid;
    isValid =
        _validateHearingThreshold(left500HzController.text, left500HzError) &&
            isValid;
    isValid =
        _validateHearingThreshold(left1000HzController.text, left1000HzError) &&
            isValid;
    isValid =
        _validateHearingThreshold(left1500HzController.text, left1500HzError, isRequired: false) &&
            isValid;
    isValid =
        _validateHearingThreshold(left2000HzController.text, left2000HzError) &&
            isValid;
    isValid =
        _validateHearingThreshold(left3000HzController.text, left3000HzError, isRequired: false) &&
            isValid;
    isValid =
        _validateHearingThreshold(left4000HzController.text, left4000HzError) &&
            isValid;
    isValid =
        _validateHearingThreshold(left6000HzController.text, left6000HzError, isRequired: false) &&
            isValid;
    isValid =
        _validateHearingThreshold(left8000HzController.text, left8000HzError) &&
            isValid;

    // Validate Right Ear Thresholds
    isValid =
        _validateHearingThreshold(right250HzController.text, right250HzError) &&
            isValid;
    isValid =
        _validateHearingThreshold(right500HzController.text, right500HzError) &&
            isValid;
    isValid = _validateHearingThreshold(
            right1000HzController.text, right1000HzError) &&
        isValid;
    isValid = _validateHearingThreshold(
            right1500HzController.text, right1500HzError, isRequired: false) &&
        isValid;
    isValid = _validateHearingThreshold(
            right2000HzController.text, right2000HzError) &&
        isValid;
    isValid = _validateHearingThreshold(
            right3000HzController.text, right3000HzError, isRequired: false) &&
        isValid;
    isValid = _validateHearingThreshold(
            right4000HzController.text, right4000HzError) &&
        isValid;
    isValid = _validateHearingThreshold(
            right6000HzController.text, right6000HzError, isRequired: false) &&
        isValid;
    isValid = _validateHearingThreshold(
            right8000HzController.text, right8000HzError) &&
        isValid;

    // // Validate WRS
    isValid = _validateWRS(leftWRSController.text, leftWRSError) && isValid;
    isValid = _validateWRS(rightWRSController.text, rightWRSError) && isValid;

    // // Validate Peak Pressure
    isValid = _validatePeakPressure(
            leftPeakPressureController.text, leftPeakPressureError) &&
        isValid;
    isValid = _validatePeakPressure(
            rightPeakPressureController.text, rightPeakPressureError) &&
        isValid;
    isValid = _validateTestDate(testDateController.text) && isValid;
    return isValid;
  }

  // Get collected audiometry data as a Map
  Map<String, String> getAudiometryData() {
    Map<String, String> data = {};
    
    // Only add non-empty fields to the data map
    if (left250HzController.text.isNotEmpty) data['Left250'] = left250HzController.text;
    if (left500HzController.text.isNotEmpty) data['Left500'] = left500HzController.text;
    if (left1000HzController.text.isNotEmpty) data['Left1000'] = left1000HzController.text;
    if (left1500HzController.text.isNotEmpty) data['Left1500'] = left1500HzController.text;
    if (left2000HzController.text.isNotEmpty) data['Left2000'] = left2000HzController.text;
    if (left3000HzController.text.isNotEmpty) data['Left3000'] = left3000HzController.text;
    if (left4000HzController.text.isNotEmpty) data['Left4000'] = left4000HzController.text;
    if (left6000HzController.text.isNotEmpty) data['Left6000'] = left6000HzController.text;
    if (left8000HzController.text.isNotEmpty) data['Left8000'] = left8000HzController.text;
    if (leftPeakPressureController.text.isNotEmpty) data['LeftPP'] = leftPeakPressureController.text;
    if (leftWRSController.text.isNotEmpty) data['LeftWRS'] = leftWRSController.text;
    if (right250HzController.text.isNotEmpty) data['Right250'] = right250HzController.text;
    if (right500HzController.text.isNotEmpty) data['Right500'] = right500HzController.text;
    if (right1000HzController.text.isNotEmpty) data['Right1000'] = right1000HzController.text;
    if (right1500HzController.text.isNotEmpty) data['Right1500'] = right1500HzController.text;
    if (right2000HzController.text.isNotEmpty) data['Right2000'] = right2000HzController.text;
    if (right3000HzController.text.isNotEmpty) data['Right3000'] = right3000HzController.text;
    if (right4000HzController.text.isNotEmpty) data['Right4000'] = right4000HzController.text;
    if (right6000HzController.text.isNotEmpty) data['Right6000'] = right6000HzController.text;
    if (right8000HzController.text.isNotEmpty) data['Right8000'] = right8000HzController.text;
    if (rightPeakPressureController.text.isNotEmpty) data['RightPP'] = rightPeakPressureController.text;
    if (rightWRSController.text.isNotEmpty) data['RightWRS'] = rightWRSController.text;
    
    // TestDate is required, so always include it
    data['TestDate'] = testDateController.text;
    
    return data;
  }

  Future<void> postAudiometryReport() async {
    try {
      isLoading.value = true;
      bool response = await _apiService.postDetails(
        type: BasicDetailTypes.Audiometry,
        detailsData: getAudiometryData(),
      );
      if (response) {
        Get.offAllNamed(MetaView.routeName);
      } else {
        reusableSnackBar(message: "Something went wrong...");
      }
    } catch (e) {
      debugPrint("Error: $e");
      reusableSnackBar(message: "Something went wrong...");
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> onSaveAndContinue() async {
    if (validateForm()) {
      await postAudiometryReport();
    } else {
      reusableSnackBar(message: "Please fill all required fields correctly");
    }
  }

  // Fetch saved audiometry data from API
  Future<void> fetchSavedAudiometryData() async {
    try {
      isLoading.value = true;
      final Map<String, dynamic>? response = await _apiService.getDetails(
        type: BasicDetailTypes.Audiometry,
      );

      if (response != null) {
        final audiometryDetails = AudiometryDetailsModel.fromJson(response);

        // Prefill left ear thresholds
        left250HzController.text = audiometryDetails.left250;
        left500HzController.text = audiometryDetails.left500;
        left1000HzController.text = audiometryDetails.left1000;
        left1500HzController.text = audiometryDetails.left1500;
        left2000HzController.text = audiometryDetails.left2000;
        left3000HzController.text = audiometryDetails.left3000;
        left4000HzController.text = audiometryDetails.left4000;
        left6000HzController.text = audiometryDetails.left6000;
        left8000HzController.text = audiometryDetails.left8000;

        // Prefill right ear thresholds
        right250HzController.text = audiometryDetails.right250;
        right500HzController.text = audiometryDetails.right500;
        right1000HzController.text = audiometryDetails.right1000;
        right1500HzController.text = audiometryDetails.right1500;
        right2000HzController.text = audiometryDetails.right2000;
        right3000HzController.text = audiometryDetails.right3000;
        right4000HzController.text = audiometryDetails.right4000;
        right6000HzController.text = audiometryDetails.right6000;
        right8000HzController.text = audiometryDetails.right8000;

        // Prefill WRS scores
        leftWRSController.text = audiometryDetails.leftWRS;
        rightWRSController.text = audiometryDetails.rightWRS;

        // Prefill tympanometry data
        leftPeakPressureController.text = audiometryDetails.leftPP;
        rightPeakPressureController.text = audiometryDetails.rightPP;

        // Prefill test date
        testDateController.text = audiometryDetails.testDate.isNotEmpty
            ? audiometryDetails.testDate
            : DateFormat('MM/dd/yyyy').format(DateTime.now());
      }
    } catch (e) {
      debugPrint("Error fetching saved audiometry data: $e");
      // Set today's date as default if there's an error
      testDateController.text = DateFormat('MM/dd/yyyy').format(DateTime.now());
    } finally {
      isLoading.value = false;
    }
  }

  // Check completion status from MetaController
  Future<void> _checkCompletionStatus() async {
    try {
      final metaController = Get.find<MetaController>();
      const routeName = AudiometryReportDataEntryView.routeName;
      final isCompleted = metaController.moduleCompletionStatus[routeName] ?? false;
      final hasCompletedGTPlusOnce = metaController.hasPatientCompletedGTPlusOnce();

      debugPrint("Audiometry route name: $routeName");
      debugPrint("Audiometry completion status: $isCompleted");
      debugPrint("Has patient completed GT+ once: $hasCompletedGTPlusOnce");
      debugPrint("All completion statuses: ${metaController.moduleCompletionStatus}");

      if (isCompleted || hasCompletedGTPlusOnce) {
        debugPrint("Setting audiometry form to read-only mode");
        enableReadOnlyMode();
      }
    } catch (e) {
      // MetaController might not be initialized yet, that's okay
      debugPrint("MetaController not found or not initialized: $e");
    }
  }

  /// Prefills the form fields with extracted audiogram data
  void prefillWithExtractedData(AudiometryDetailsModel data) {
    // Reset the form before prefilling
    resetForm();
    // Left ear thresholds
    if (data.left250.isNotEmpty) left250HzController.text = data.left250;
    if (data.left500.isNotEmpty) left500HzController.text = data.left500;
    if (data.left1000.isNotEmpty) left1000HzController.text = data.left1000;
    if (data.left1500.isNotEmpty) left1500HzController.text = data.left1500;
    if (data.left2000.isNotEmpty) left2000HzController.text = data.left2000;
    if (data.left3000.isNotEmpty) left3000HzController.text = data.left3000;
    if (data.left4000.isNotEmpty) left4000HzController.text = data.left4000;
    if (data.left6000.isNotEmpty) left6000HzController.text = data.left6000;
    if (data.left8000.isNotEmpty) left8000HzController.text = data.left8000;

    // Right ear thresholds
    if (data.right250.isNotEmpty) right250HzController.text = data.right250;
    if (data.right500.isNotEmpty) right500HzController.text = data.right500;
    if (data.right1000.isNotEmpty) right1000HzController.text = data.right1000;
    if (data.right1500.isNotEmpty) right1500HzController.text = data.right1500;
    if (data.right2000.isNotEmpty) right2000HzController.text = data.right2000;
    if (data.right3000.isNotEmpty) right3000HzController.text = data.right3000;
    if (data.right4000.isNotEmpty) right4000HzController.text = data.right4000;
    if (data.right6000.isNotEmpty) right6000HzController.text = data.right6000;
    if (data.right8000.isNotEmpty) right8000HzController.text = data.right8000;

    // Word recognition scores - Extract just the percentage value
    if (data.leftWRS.isNotEmpty) {
      leftWRSController.text = _extractPercentageValue(data.leftWRS);
    }
    if (data.rightWRS.isNotEmpty) {
      rightWRSController.text = _extractPercentageValue(data.rightWRS);
    }

    // Tympanometry
    if (data.leftPP.isNotEmpty) leftPeakPressureController.text = data.leftPP;
    if (data.rightPP.isNotEmpty) {
      rightPeakPressureController.text = data.rightPP;
    }

    // Switch to edit mode after extraction
    isReadOnly.value = false;
    // Validate the form
    validateForm();
  }

  /// Extracts just the percentage value from strings like "44% at 95db"
  String _extractPercentageValue(String wrsValue) {
    // Try to find the percentage value
    if (wrsValue.contains('%')) {
      // Extract just the number before the % sign
      final percentRegex = RegExp(r'(\d+)%');
      final match = percentRegex.firstMatch(wrsValue);
      if (match != null && match.groupCount >= 1) {
        return match.group(1) ?? '';
      }
    }

    // If we can't find a percentage, try to see if it's just a number
    final numericRegex = RegExp(r'^\d+$');
    if (numericRegex.hasMatch(wrsValue)) {
      return wrsValue;
    }

    // Return the original value if we can't extract anything
    return wrsValue;
  }
}
