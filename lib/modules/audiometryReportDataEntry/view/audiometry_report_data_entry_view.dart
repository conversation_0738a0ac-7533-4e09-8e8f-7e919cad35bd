import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../utils/date_input_fomatter.dart';
import '../controller/audiometry_report_data_entry_controller.dart';

import '../../../utils/appConst/app_colors.dart';
import '../../../utils/reusableWidgets/custom_text_field_for_app.dart';
import '../../../utils/reusableWidgets/reusable_app_bar.dart';
import '../../../utils/reusableWidgets/reusable_button.dart';
import '../../../utils/reusableWidgets/reusable_footer.dart';
import '../../gemini_image_to_text/gemini_report_card.dart';
import '../../../models/audiometry_details_model.dart';
import '../../../utils/reusableWidgets/gemini_analysis_card.dart';
import '../../../utils/reusableWidgets/gemini_bottom_sheet.dart';

class AudiometryReportDataEntryView
    extends GetView<AudiometryReportDataEntryController> {
  const AudiometryReportDataEntryView({super.key});

  static const String routeName = "/AudiometryReportDataEntryView";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: reusableAppBar(context: context),
      bottomNavigationBar: const ReusableFooter(),
      body: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    return Obx(() {
      if (controller.isInitializing.value) {
        return const Center(child: CircularProgressIndicator());
      }

      return SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: context.width * .04),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: context.height * .04),
              _buildSectionTitle("Audiometry Report Data Entry", 24),
              SizedBox(height: context.height * .02),
              _buildReadOnlyIndicator(),
              _buildGeminiButton(context),
              const SizedBox(height: 32),
              _buildLeftEarThresholdSection(context),
              const SizedBox(height: 32),
              _buildRightEarThresholdSection(context),
              const SizedBox(height: 32),
              _buildWordRecognitionSection(context),
              const SizedBox(height: 32),
              _buildTympanometrySection(context),
              const SizedBox(height: 32),
              _buildTestDateSection(context),
              const SizedBox(height: 32),
              _buildActionButtons(context),
              const SizedBox(height: 32),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildSectionTitle(String title, double fontSize) {
    return Text(
      title,
      style: TextStyle(
        color: AppColors.charcoalBlue,
        fontSize: fontSize,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildReadOnlyIndicator() {
    return Obx(() {
      if (controller.isReadOnly.value) {
        // Read-Only Mode Indicator
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            border: Border.all(color: Colors.blue.shade200),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                Icons.lock_outline,
                color: Colors.blue.shade600,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  "Read-Only Mode: This form has been completed. Use 'Edit' to make changes or 'Reset' to start over.",
                  style: TextStyle(
                    color: Colors.blue.shade700,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        );
      } else if (controller.isFormCompleted.value && !controller.isReadOnly.value) {
        // Edit Mode Indicator (for previously completed forms)
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.orange.shade50,
            border: Border.all(color: Colors.orange.shade200),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                Icons.edit_outlined,
                color: Colors.orange.shade600,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  "Edit Mode: You are now editing a previously completed form. Changes will be saved when you submit.",
                  style: TextStyle(
                    color: Colors.orange.shade700,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        );
      } else {
        return const SizedBox.shrink();
      }
    });
  }

  Widget _buildGeminiButton(BuildContext context) {
    return GeminiAnalysisCard(
      title: 'AI-Powered Audiogram Analysis',
      reportType: 'audiogram',
      onTap: () => _showGeminiBottomSheet(context),
    );
  }

  /// Shows a bottom sheet with the Gemini audiogram analysis tool
  void _showGeminiBottomSheet(BuildContext context) {
    showGeminiBottomSheet(
      context: context,
      title: 'AI Audiogram Analysis',
      reportType: ReportType.audiometry,
      toolTitle: 'Audiogram Report Scanner',
      description: 'Extract Audiogram Data with AI',
      onDataExtracted: (data) => controller.prefillWithExtractedData(data as AudiometryDetailsModel),
    );
  }

  Widget _buildLeftEarThresholdSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle("Left Ear Hearing Threshold Levels (dB)", 18),
        const SizedBox(height: 8),
        const Text(
          "(Values can include negative sign and decimal point)",
          style: TextStyle(
            color: AppColors.charcoalBlue,
            fontSize: 14,
            fontStyle: FontStyle.italic,
          ),
        ),
        const SizedBox(height: 16),
        _buildFrequencyRow1Left(context),
        const SizedBox(height: 24),
        _buildFrequencyRow2Left(context),
        const SizedBox(height: 24),
        _buildFrequencyRow3Left(context),
      ],
    );
  }

  Widget _buildFrequencyRow1Left(BuildContext context) {
    return Obx(() => Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomTextFieldForApp(
              textEditingController: controller.left250HzController,
              hintText: "Enter value",
              title: "250Hz",
              maxLength: 7,
              errorText: controller.left250HzError.value,
              textInputType: const TextInputType.numberWithOptions(
                  decimal: true, signed: true),
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
            const SizedBox(width: 16),
            CustomTextFieldForApp(
              textEditingController: controller.left500HzController,
              hintText: "Enter value",
              title: "500Hz",
              maxLength: 7,
              errorText: controller.left500HzError.value,
              textInputType: const TextInputType.numberWithOptions(
                  decimal: true, signed: true),
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
            const SizedBox(width: 16),
            CustomTextFieldForApp(
              textEditingController: controller.left1000HzController,
              hintText: "Enter value",
              title: "1000Hz",
              maxLength: 7,
              errorText: controller.left1000HzError.value,
              textInputType: const TextInputType.numberWithOptions(
                  decimal: true, signed: true),
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
          ],
        ));
  }

  Widget _buildFrequencyRow2Left(BuildContext context) {
    return Obx(() => Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomTextFieldForApp(
              textEditingController: controller.left1500HzController,
              hintText: "Enter value",
              title: "1500Hz",
              maxLength: 7,
              isRequired: false,
              errorText: controller.left1500HzError.value,
              textInputType: const TextInputType.numberWithOptions(
                  decimal: true, signed: true),
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
            const SizedBox(width: 16),
            CustomTextFieldForApp(
              textEditingController: controller.left2000HzController,
              hintText: "Enter value",
              title: "2000Hz",
              maxLength: 7,
              errorText: controller.left2000HzError.value,
              textInputType: const TextInputType.numberWithOptions(
                  decimal: true, signed: true),
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
            const SizedBox(width: 16),
            CustomTextFieldForApp(
              textEditingController: controller.left3000HzController,
              hintText: "Enter value",
              title: "3000Hz",
              maxLength: 7,
              isRequired: false,
              errorText: controller.left3000HzError.value,
              textInputType: const TextInputType.numberWithOptions(
                  decimal: true, signed: true),
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
          ],
        ));
  }
  
  Widget _buildFrequencyRow3Left(BuildContext context) {
    return Obx(() => Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomTextFieldForApp(
              textEditingController: controller.left4000HzController,
              hintText: "Enter value",
              title: "4000Hz",
              maxLength: 7,
              errorText: controller.left4000HzError.value,
              textInputType: const TextInputType.numberWithOptions(
                  decimal: true, signed: true),
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
            const SizedBox(width: 16),
            CustomTextFieldForApp(
              textEditingController: controller.left6000HzController,
              hintText: "Enter value",
              title: "6000Hz",
              maxLength: 7,
              isRequired: false,
              errorText: controller.left6000HzError.value,
              textInputType: const TextInputType.numberWithOptions(
                  decimal: true, signed: true),
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
            const SizedBox(width: 16),
            CustomTextFieldForApp(
              textEditingController: controller.left8000HzController,
              hintText: "Enter value",
              title: "8000Hz",
              maxLength: 7,
              errorText: controller.left8000HzError.value,
              textInputType: const TextInputType.numberWithOptions(
                  decimal: true, signed: true),
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
          ],
        ));
  }

  Widget _buildRightEarThresholdSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle("Right Ear Hearing Threshold Levels (dB)", 18),
        const SizedBox(height: 8),
        const Text(
          "(Values can include negative sign and decimal point)",
          style: TextStyle(
            color: AppColors.charcoalBlue,
            fontSize: 14,
            fontStyle: FontStyle.italic,
          ),
        ),
        const SizedBox(height: 16),
        _buildFrequencyRow1Right(context),
        const SizedBox(height: 24),
        _buildFrequencyRow2Right(context),
        const SizedBox(height: 24),
        _buildFrequencyRow3Right(context),
      ],
    );
  }

  Widget _buildFrequencyRow1Right(BuildContext context) {
    return Obx(() => Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomTextFieldForApp(
              textEditingController: controller.right250HzController,
              hintText: "Enter value",
              title: "250Hz",
              maxLength: 7,
              errorText: controller.right250HzError.value,
              textInputType: const TextInputType.numberWithOptions(
                  decimal: true, signed: true),
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
            const SizedBox(width: 16),
            CustomTextFieldForApp(
              textEditingController: controller.right500HzController,
              hintText: "Enter value",
              title: "500Hz",
              maxLength: 7,
              errorText: controller.right500HzError.value,
              textInputType: const TextInputType.numberWithOptions(
                  decimal: true, signed: true),
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
            const SizedBox(width: 16),
            CustomTextFieldForApp(
              textEditingController: controller.right1000HzController,
              hintText: "Enter value",
              title: "1000Hz",
              maxLength: 7,
              errorText: controller.right1000HzError.value,
              textInputType: const TextInputType.numberWithOptions(
                  decimal: true, signed: true),
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
          ],
        ));
  }

  Widget _buildFrequencyRow2Right(BuildContext context) {
    return Obx(() => Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomTextFieldForApp(
              textEditingController: controller.right1500HzController,
              hintText: "Enter value",
              title: "1500Hz",
              maxLength: 7,
              isRequired: false,
              errorText: controller.right1500HzError.value,
              textInputType: const TextInputType.numberWithOptions(
                  decimal: true, signed: true),
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
            const SizedBox(width: 16),
            CustomTextFieldForApp(
              textEditingController: controller.right2000HzController,
              hintText: "Enter value",
              title: "2000Hz",
              maxLength: 7,
              errorText: controller.right2000HzError.value,
              textInputType: const TextInputType.numberWithOptions(
                  decimal: true, signed: true),
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
            const SizedBox(width: 16),
            CustomTextFieldForApp(
              textEditingController: controller.right3000HzController,
              hintText: "Enter value",
              title: "3000Hz",
              maxLength: 7,
              isRequired: false,
              errorText: controller.right3000HzError.value,
              textInputType: const TextInputType.numberWithOptions(
                  decimal: true, signed: true),
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
          ],
        ));
  }
  
  Widget _buildFrequencyRow3Right(BuildContext context) {
    return Obx(() => Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomTextFieldForApp(
              textEditingController: controller.right4000HzController,
              hintText: "Enter value",
              title: "4000Hz",
              maxLength: 7,
              errorText: controller.right4000HzError.value,
              textInputType: const TextInputType.numberWithOptions(
                  decimal: true, signed: true),
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
            const SizedBox(width: 16),
            CustomTextFieldForApp(
              textEditingController: controller.right6000HzController,
              hintText: "Enter value",
              title: "6000Hz",
              maxLength: 7,
              isRequired: false,
              errorText: controller.right6000HzError.value,
              textInputType: const TextInputType.numberWithOptions(
                  decimal: true, signed: true),
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
            const SizedBox(width: 16),
            CustomTextFieldForApp(
              textEditingController: controller.right8000HzController,
              hintText: "Enter value",
              title: "8000Hz",
              maxLength: 7,
              errorText: controller.right8000HzError.value,
              textInputType: const TextInputType.numberWithOptions(
                  decimal: true, signed: true),
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
          ],
        ));
  }

  Widget _buildWordRecognitionSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle("Word Recognition Scores (WRS)", 18),
        const SizedBox(height: 16),
        _buildWRSRow(context),
      ],
    );
  }

  Widget _buildWRSRow(BuildContext context) {
    return Obx(() => Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomTextFieldForApp(
              textEditingController: controller.leftWRSController,
              hintText: "0-100%",
              title: "Left Ear WRS",
              maxLength: 3,
              isRequired: false,
              errorText: controller.leftWRSError.value,
              textInputType: TextInputType.number,
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
            const SizedBox(width: 16),
            CustomTextFieldForApp(
              textEditingController: controller.rightWRSController,
              hintText: "0-100%",
              maxLength: 3,
              title: "Right Ear WRS",
              isRequired: false,
              errorText: controller.rightWRSError.value,
              textInputType: TextInputType.number,
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
            const SizedBox(width: 16),
            Expanded(child: Container()),
          ],
        ));
  }

  Widget _buildTympanometrySection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle("Tympanometry", 18),
        const SizedBox(height: 16),
        _buildTympanometryRow(context),
      ],
    );
  }

  Widget _buildTympanometryRow(BuildContext context) {
    return Obx(() => Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomTextFieldForApp(
              textEditingController: controller.leftPeakPressureController,
              hintText: "Enter value",
              title: "Left Ear Peak Pressure (daPa)",
              maxLength: 4,
              isRequired: false,
              errorText: controller.leftPeakPressureError.value,
              textInputType: const TextInputType.numberWithOptions(
                  signed: true, decimal: true),
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
            const SizedBox(width: 16),
            CustomTextFieldForApp(
              textEditingController: controller.rightPeakPressureController,
              hintText: "Enter value",
              title: "Right Ear Peak Pressure (daPa)",
              isRequired: false,
              maxLength: 4,
              errorText: controller.rightPeakPressureError.value,
              textInputType: const TextInputType.numberWithOptions(
                  signed: true, decimal: true),
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
            const SizedBox(width: 16),
            Expanded(child: Container()),
          ],
        ));
  }

  Widget _buildTestDateSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle("Test Information", 18),
        const SizedBox(height: 16),
        _buildTestDateRow(context),
      ],
    );
  }

  Widget _buildTestDateRow(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Obx(
          () => CustomTextFieldForApp(
            textEditingController: controller.testDateController,
            hintText: "mm/dd/yyyy",
            inputFormatters: [DateInputFormatter()],
            title: "Test Date",
            textInputType: TextInputType.datetime,
            errorText: controller.testDateError.value,
            readOnly: controller.isReadOnly.value,
            onChanged: (value) {
              controller.validateForm();
            },
          ),
        ),
        const SizedBox(width: 16),
        Expanded(child: Container()),
        Expanded(child: Container()),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Obx(() {
      if (controller.isReadOnly.value) {
        // Show Reset and Edit buttons when in read-only mode
        return Row(
          children: [
            ReusableButton(
              width: 140,
              title: 'Reset',
              color: Colors.red,
              borderColor: Colors.red,
              onTap: () {
                controller.resetForm();
              },
            ),
            const SizedBox(width: 16),
            ReusableButton(
              width: 140,
              title: 'Edit',
              onTap: () {
                controller.enableEditMode();
              },
            ),
          ],
        );
      } else {
        // Show Submit button when in edit mode
        return ReusableButton(
          width: 140,
          title: 'Submit',
          isLoading: controller.isLoading.value,
          onTap: controller.onSaveAndContinue,
        );
      }
    });
  }
}
