import 'package:get/get.dart';
import 'package:gt_plus/modules/audiometryReportDataEntry/controller/audiometry_report_data_entry_controller.dart';
import 'package:gt_plus/services/gemini_service.dart';

/// Binding for the Audiometry Report Data Entry module
/// Ensures controllers and services are properly registered
class AudiometryReportDataEntryBinding {
  static List<Bindings> binding = [
    BindingsBuilder(() {
      // Register the main controller
      Get.put(AudiometryReportDataEntryController());
      
      // Ensure GeminiService is initialized and available for AI data extraction
      if (!Get.isRegistered<GeminiService>()) {
        Get.putAsync(() => GeminiService().init());
      }
    }),
  ];
}
