import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gt_plus/models/extra_patient_data_model.dart';

import '../../../services/api_service.dart';
import '../../../utils/reusableWidgets/resusable_snackbar.dart';
import '../../meta/view/meta_view.dart';

class ExtraPatientDataController extends GetxController {
  final items = <RxMap<String, TextEditingController>>[].obs;

  // Error tracking
  final nameErrors = <RxString>[].obs;
  final valueErrors = <RxString>[].obs;

  // Loading state
  RxBool isLoading = false.obs;
  final ApiService _apiService = ApiService();

  @override
  void onInit() {
    super.onInit();
    addNewRow();
    fetchSavedExtraPatientData();
  }

  @override
  void onClose() {
    // Dispose all controllers
    for (var item in items) {
      item['name']?.dispose();
      item['value']?.dispose();
    }
    super.onClose();
  }

  // Add a new row with empty controllers
  void addNewRow() {
    final nameController = TextEditingController();
    final valueController = TextEditingController();

    items.add({
      'name': nameController,
      'value': valueController,
    }.obs);

    nameErrors.add(''.obs);
    valueErrors.add(''.obs);
  }

  // Remove a row at specific index
  void removeRow(int index) {
    if (items.length <= 1) {
      reusableSnackBar(message: "At least 1 row is required");
      return;
    }

    // Dispose controllers to prevent memory leaks
    items[index]['name']?.dispose();
    items[index]['value']?.dispose();

    items.removeAt(index);
    nameErrors.removeAt(index);
    valueErrors.removeAt(index);
  }

  // Validate a field is not empty
  bool _validateNotEmpty(String value, RxString errorField) {
    if (value.isEmpty) {
      errorField.value = 'This field is required';
      return false;
    }
    errorField.value = '';
    return true;
  }

  // Validate all fields
  bool validateForm() {
    bool isValid = true;

    for (int i = 0; i < items.length; i++) {
      final nameValid =
          _validateNotEmpty(items[i]['name']?.text ?? '', nameErrors[i]);

      final valueValid =
          _validateNotEmpty(items[i]['value']?.text ?? '', valueErrors[i]);

      isValid = isValid && nameValid && valueValid;
    }

    return isValid;
  }

  // Get data in format ready for API
  Map<String, dynamic> getExtraPatientData() {
    final List<Map<String, dynamic>> itemsList = [];

    for (var item in items) {
      itemsList.add({
        'name': item['name']?.text ?? '',
        'value': item['value']?.text ?? '',
      });
    }

    return {
      'items': itemsList,
    };
  }

  // Save and continue
  Future<void> onSaveAndContinue() async {
    if (validateForm()) {
      await postReport();
    } else {
      reusableSnackBar(message: "Please fill all required fields correctly");
    }
  }

  // Post report to API
  Future<void> postReport() async {
    try {
      isLoading.value = true;
      bool response = await _apiService.postDetails(
        type: BasicDetailTypes.ExtraData,
        detailsData: getExtraPatientData(),
      );
      if (response) {
        Get.offAllNamed(MetaView.routeName);
      } else {
        reusableSnackBar(message: "Something went wrong...");
      }
    } catch (e) {
      debugPrint("Error: $e");
      reusableSnackBar(message: "Something went wrong...");
    } finally {
      isLoading.value = false;
    }
  }

  // Fetch saved extra patient data from API
  Future<void> fetchSavedExtraPatientData() async {
    try {
      isLoading.value = true;
      final Map<String, dynamic>? response = await _apiService.getDetails(
        type: BasicDetailTypes.ExtraData,
      );

      if (response != null) {
        final extraData = ExtraPatientDataModel.fromJson(response);

        // Clear default rows
        for (var item in items) {
          item['name']?.dispose();
          item['value']?.dispose();
        }
        items.clear();
        nameErrors.clear();
        valueErrors.clear();

        // Add rows from saved data
        if (extraData.items.isNotEmpty) {
          for (var item in extraData.items) {
            final nameController = TextEditingController(text: item.name);
            final valueController = TextEditingController(text: item.value);
            items.add({
              'name': nameController,
              'value': valueController,
            }.obs);
            nameErrors.add(''.obs);
            valueErrors.add(''.obs);
          }
        } else {
          addNewRow();
        }
      }
    } catch (e) {
      debugPrint("Error fetching saved extra patient data: $e");
    } finally {
      isLoading.value = false;
    }
  }
}
