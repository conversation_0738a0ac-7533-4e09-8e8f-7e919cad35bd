import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controller/extra_patient_data_controller.dart';

import '../../../utils/appConst/app_colors.dart';
import '../../../utils/reusableWidgets/custom_text_field_for_app.dart';
import '../../../utils/reusableWidgets/reusable_app_bar.dart';
import '../../../utils/reusableWidgets/reusable_button.dart';
import '../../../utils/reusableWidgets/reusable_footer.dart';

class ExtraPatientDataView extends GetView<ExtraPatientDataController> {
  const ExtraPatientDataView({super.key});

  static const String routeName = "/ExtraPatientDataView";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: reusableAppBar(context: context),
      bottomNavigationBar: const ReusableFooter(),
      body: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: context.width * .04),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: context.height * .04),
            _buildSectionTitle("Extra Patient Data", 24),
            SizedBox(height: context.height * .02),
            const SizedBox(height: 32),
            _buildExtraDataSection(context),
            const SizedBox(height: 32),
            _buildSubmitButton(context),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title, double fontSize) {
    return Text(
      title,
      style: TextStyle(
        color: AppColors.charcoalBlue,
        fontSize: fontSize,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildExtraDataSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle("Patient Additional Information", 18),
        const SizedBox(height: 16),
        _buildDataTable(context),
        const SizedBox(height: 16),
        _buildAddRowButton(context),
      ],
    );
  }

  Widget _buildDataTable(BuildContext context) {
    return Obx(() => Column(
          children: [
            // Table headers
            Container(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              decoration: BoxDecoration(
                color: AppColors.charcoalBlue.withOpacity(0.1),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: const Row(
                children: [
                  SizedBox(
                    width: 40,
                    child: Text(
                      "S.No",
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: AppColors.charcoalBlue,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 5,
                    child: Text(
                      "Name",
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: AppColors.charcoalBlue,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 5,
                    child: Text(
                      "Value",
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: AppColors.charcoalBlue,
                      ),
                    ),
                  ),
                  SizedBox(width: 50), // Space for delete button
                ],
              ),
            ),
            // Table rows
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(8),
                  bottomRight: Radius.circular(8),
                ),
              ),
              child: ListView.builder(
                physics: const NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                itemCount: controller.items.length,
                itemBuilder: (context, index) {
                  return _buildDataRow(context, index);
                },
              ),
            ),
          ],
        ));
  }

  Widget _buildDataRow(BuildContext context, int index) {
    return Obx(() => Container(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
          decoration: BoxDecoration(
            border: index < controller.items.length - 1
                ? Border(bottom: BorderSide(color: Colors.grey.shade300))
                : null,
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 40,
                alignment: Alignment.center,
                margin: const EdgeInsets.only(top: 12),
                child: Text(
                  "${index + 1}",
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: AppColors.charcoalBlue,
                  ),
                ),
              ),
              Expanded(
                flex: 5,
                child: Container(
                  margin: const EdgeInsets.only(right: 8),
                  child: _buildTextField(
                    controller.items[index]['name']!,
                    'Name',
                    controller.nameErrors[index],
                  ),
                ),
              ),
              Expanded(
                flex: 5,
                child: Container(
                  margin: const EdgeInsets.only(right: 8),
                  child: _buildTextField(
                    controller.items[index]['value']!,
                    'Value',
                    controller.valueErrors[index],
                  ),
                ),
              ),
              SizedBox(
                width: 40,
                child: IconButton(
                  icon: const Icon(Icons.delete_outline, color: Colors.red),
                  onPressed: () => controller.removeRow(index),
                  tooltip: "Remove row",
                ),
              ),
            ],
          ),
        ));
  }

  Widget _buildTextField(
    TextEditingController textController,
    String hint,
    RxString errorText,
  ) {
    return Obx(() => TextField(
          controller: textController,
          decoration: InputDecoration(
            hintText: hint,
            errorText: errorText.value.isEmpty ? null : errorText.value,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppColors.charcoalBlue),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red),
            ),
          ),
          onChanged: (value) => controller.validateForm(),
        ));
  }

  Widget _buildAddRowButton(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: controller.addNewRow,
      icon: const Icon(Icons.add_circle_outline),
      label: const Text("Add Row"),
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.charcoalBlue,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  Widget _buildSubmitButton(BuildContext context) {
    return Obx(() => ReusableButton(
          width: 150,
          title: 'Submit',
          isLoading: controller.isLoading.value,
          onTap: controller.onSaveAndContinue,
        ));
  }
} 