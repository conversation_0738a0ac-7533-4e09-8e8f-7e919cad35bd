import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import '../../models/medical_report_model.dart';
import 'controller/gemini_report_controller.dart';

/// Enum to define different report types for the Gemini AI analysis
enum ReportType {
  audiometry,
  cognivue,
}

/// A reusable Gemini AI-powered card for medical report image analysis.
/// Handles different report types like audiogram and cognivue.
class GeminiReportCard extends StatelessWidget {
  /// The type of report to be analyzed
  final ReportType reportType;

  /// Callback function that receives the extracted data
  final Function(MedicalReportModel data)? onDataExtracted;

  /// Title for the AI tool display
  final String toolTitle;

  /// Description for the AI tool
  final String description;

  const GeminiReportCard({
    super.key,
    required this.reportType,
    this.onDataExtracted,
    this.toolTitle = 'Gemini AI Tools',
    this.description = 'Extract Data with Gemini AI',
  });

  @override
  Widget build(BuildContext context) {
    return GetBuilder<GeminiReportController>(
        init: GeminiReportController(reportType: reportType),
        builder: (controller) {
          return Container(
            decoration: BoxDecoration(
              border: Border.all(
                color: Colors.deepOrange,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Stack(
              alignment: Alignment.topRight,
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "$toolTitle (Beta)",
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.deepOrange,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        "You can take a live picture of the ${reportType == ReportType.audiometry ? 'Audiogram' : 'Cognivue'} report or upload it from the iPad.",
                        style: const TextStyle(
                          color: Colors.black87,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Image preview (show if an image is selected)
                      Obx(() => controller.selectedImage.value != null
                          ? _buildImagePreview(controller.selectedImage.value!)
                          : const SizedBox.shrink()),

                      const SizedBox(height: 16),

                      // Option buttons
                      _buildImageSourceOptions(controller),

                      const SizedBox(height: 16),

                      // Process button - only show after image selection
                      Obx(() => controller.selectedImage.value != null
                          ? _buildProcessButton(
                              isLoading: controller.isLoading.value,
                              onTap: () =>
                                  controller.processImage(onDataExtracted),
                              description: description,
                            )
                          : const SizedBox.shrink()),

                      // Error message
                      Obx(() => controller.errorMessage.value.isNotEmpty
                          ? Padding(
                              padding: const EdgeInsets.only(top: 16),
                              child: Text(
                                controller.errorMessage.value,
                                style: const TextStyle(
                                    color: Colors.red, fontSize: 14),
                                textAlign: TextAlign.center,
                              ),
                            )
                          : const SizedBox.shrink()),
                    ],
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Get.back(),
                ),
              ],
            ),
          );
        });
  }

  /// Builds the image preview widget
  Widget _buildImagePreview(File imageFile) {
    return Container(
      height: 200,
      width: double.infinity,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Image.file(imageFile, fit: BoxFit.contain),
      ),
    );
  }

  /// Builds the row with camera and gallery option buttons
  Widget _buildImageSourceOptions(GeminiReportController controller) {
    return Row(
      children: [
        Expanded(
          child: _buildOptionButton(
            icon: Icons.camera_alt,
            label: 'Take Photo',
            onTap: () => controller.pickImage(ImageSource.camera),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildOptionButton(
            icon: Icons.photo_library,
            label: 'Choose File',
            onTap: () => controller.pickImage(ImageSource.gallery),
          ),
        ),
      ],
    );
  }

  Widget _buildOptionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: Colors.orange.shade50,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: Colors.deepOrange.shade300,
              size: 24,
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProcessButton({
    required bool isLoading,
    required VoidCallback onTap,
    required String description,
  }) {
    return InkWell(
      onTap: isLoading ? null : onTap,
      child: Container(
        height: 48,
        width: double.infinity,
        decoration: BoxDecoration(
          color: const Color(0xffF26511),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: const Color(0xffF26511),
            width: 1,
          ),
        ),
        child: Center(
          child: isLoading
              ? const SizedBox(
                  height: 24,
                  width: 24,
                  child: CircularProgressIndicator(
                    strokeWidth: 2.5,
                    color: Colors.white,
                  ),
                )
              : Text(
                  description,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
        ),
      ),
    );
  }
}
