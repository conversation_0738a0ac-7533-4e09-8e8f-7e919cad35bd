import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import '../../../models/medical_report_model.dart';
import '../../../services/gemini_service.dart';
import '../../../utils/logger.dart';
import '../gemini_report_card.dart';

/// Controller for the GeminiReportCard
class GeminiReportController extends GetxController {
  final GeminiService _geminiService = Get.find<GeminiService>();
  final ReportType reportType;
  late final AppLogger _logger;

  GeminiReportController({required this.reportType}) {
    _logger = AppLogger('REPORT_CARD');
  }

  final Rx<File?> selectedImage = Rx<File?>(null);
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;

  /// Select an image from camera or gallery
  Future<void> pickImage(ImageSource source) async {
    try {
      final picker = ImagePicker();
      final pickedImage = await picker.pickImage(source: source);
      if (pickedImage != null) {
        selectedImage.value = File(pickedImage.path);
        errorMessage.value = '';
      }
    } catch (e) {
      errorMessage.value = 'Failed to pick image: ${e.toString()}';
      _logger.error('Error picking image: $e');
    }
  }

  /// Process the selected image and extract report data
  Future<void> processImage(
      Function(MedicalReportModel data)? onDataExtracted) async {
    if (selectedImage.value == null) {
      errorMessage.value = 'Please select an image first';
      return;
    }

    isLoading.value = true;
    errorMessage.value = '';

    try {
      final reportTypeName = reportType.name;
      _logger.info(
          'Processing $reportTypeName image from: ${selectedImage.value!.path}');
      _logger.info(
          'Image exists: ${await selectedImage.value!.exists()}, Size: ${await selectedImage.value!.length()} bytes');

      // Process image with generic method
      final data = await _geminiService.processReportImageFile(
          selectedImage.value!, reportType);

      if (data == null) {
        _logger.error('Received null data from Gemini service');
        errorMessage.value =
            'Failed to extract data from the image. Please try with a clearer image.';
        isLoading.value = false;
        return;
      }

      _logger.info(
          '${reportTypeName.toUpperCase()} DATA CHECK: Has valid data: ${data.hasValidData}');

      if (!data.hasValidData) {
        _logger.error('No valid data extracted');
        errorMessage.value =
            'No valid data could be extracted from the image. Please try with a clearer image.';
        isLoading.value = false;
        return;
      }

      // Call the callback with the extracted data
      if (onDataExtracted != null) {
        _logger.success('Calling onDataExtracted callback');
        onDataExtracted(data);
      }

      // Close the bottom sheet
      Get.back();
    } catch (e) {
      _logger.error('Exception during processing: $e');
      errorMessage.value = 'An error occurred: ${e.toString()}';
    } finally {
      isLoading.value = false;
    }
  }
}
