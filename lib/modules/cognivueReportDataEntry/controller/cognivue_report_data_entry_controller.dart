import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gt_plus/models/cognivue_details_model.dart';
import 'package:intl/intl.dart';

import '../../../services/api_service.dart';
import '../../../utils/reusableWidgets/resusable_snackbar.dart';
import '../../../utils/validators/date_validator.dart';
import '../../meta/view/meta_view.dart';
import '../../meta/controller/meta_controller.dart';
import '../view/cognivue_report_data_entry_view.dart';

class CognivueReportDataEntryController extends GetxController {
  // Score and Measurement Controllers
  final memoryScoreController = TextEditingController();
  final processingSpeedController = TextEditingController();
  final reactionTimeController = TextEditingController();
  final executiveFunctionController = TextEditingController();
  final visuospatialScoreController = TextEditingController();
  final testDateController = TextEditingController();

  // Error messages for validation
  final memoryScoreError = ''.obs;
  final processingSpeedError = ''.obs;
  final reactionTimeError = ''.obs;
  final executiveFunctionError = ''.obs;
  final visuospatialScoreError = ''.obs;
  final testDateError = ''.obs;

  RxBool isLoading = false.obs;
  final ApiService _apiService = ApiService();

  // Read-only mode management
  final RxBool isReadOnly = false.obs;
  final RxBool isFormCompleted = false.obs;
  final RxBool isInitializing = true.obs;

  @override
  void onInit() {
    super.onInit();
    // Initialize form with completion check first
    _initializeForm();
  }

  Future<void> _initializeForm() async {
    try {
      // Check completion status immediately
      await _checkCompletionStatus();

      // Then fetch saved data
      await fetchSavedCognivueData();
    } finally {
      // Mark initialization as complete
      isInitializing.value = false;
    }
  }

  // Method to enable read-only mode
  void enableReadOnlyMode() {
    isReadOnly.value = true;
    isFormCompleted.value = true;
  }

  // Method to enable edit mode
  void enableEditMode() {
    isReadOnly.value = false;
  }

  // Method to reset form
  void resetForm() {
    // Clear all controllers
    memoryScoreController.clear();
    processingSpeedController.clear();
    reactionTimeController.clear();
    executiveFunctionController.clear();
    visuospatialScoreController.clear();
    testDateController.clear();

    // Reset observables
    isReadOnly.value = false;
    isFormCompleted.value = false;

    // Clear all error messages
    memoryScoreError.value = '';
    processingSpeedError.value = '';
    reactionTimeError.value = '';
    executiveFunctionError.value = '';
    visuospatialScoreError.value = '';
    testDateError.value = '';
  }

  @override
  void onClose() {
    // Dispose all controllers
    memoryScoreController.dispose();
    processingSpeedController.dispose();
    reactionTimeController.dispose();
    executiveFunctionController.dispose();
    visuospatialScoreController.dispose();
    testDateController.dispose();

    super.onClose();
  }

  // Validate memory score and visuospatial score (0-100)
  bool _validateScore(String value, RxString errorField) {
    if (value.isEmpty) {
      errorField.value = 'This field is required';
      return false;
    }

    try {
      int score = int.parse(value);
      if (score < 0 || score > 100) {
        errorField.value = 'Value must be between 0-100';
        return false;
      }
      errorField.value = '';
      return true;
    } catch (e) {
      errorField.value = 'Please enter a valid number';
      return false;
    }
  }

  // Validate processing speed (100-1000 ms)
  bool _validateProcessingSpeed(String value, RxString errorField) {
    if (value.isEmpty) {
      errorField.value = 'This field is required';
      return false;
    }

    try {
      int speed = int.parse(value);
      if (speed < 100 || speed > 2500) {
        errorField.value = 'Value must be between 100-2500 ms';
        return false;
      }
      errorField.value = '';
      return true;
    } catch (e) {
      errorField.value = 'Please enter a valid number';
      return false;
    }
  }

  // Validate reaction time (0-2000+ ms)
  bool _validateReactionTime(String value, RxString errorField) {
    if (value.isEmpty) {
      errorField.value = 'This field is required';
      return false;
    }

    try {
      int time = int.parse(value);
      if (time < 100 || time > 2000) {
        errorField.value = 'Value must be between 100-2000 ms';
        return false;
      }
      errorField.value = '';
      return true;
    } catch (e) {
      errorField.value = 'Please enter a valid number';
      return false;
    }
  }

  // Validate executive function score (0-100)
  bool _validateExecutiveFunction(String value, RxString errorField) {
    if (value.isEmpty) {
      errorField.value = 'This field is required';
      return false;
    }

    try {
      int score = int.parse(value);
      if (score < 0 || score > 100) {
        errorField.value = 'Value must be between 0-100';
        return false;
      }
      errorField.value = '';
      return true;
    } catch (e) {
      errorField.value = 'Please enter a valid number';
      return false;
    }
  }

  bool _validateTestDate(String dobText) {
    return DateValidator.validateTestDate(dobText, testDateError, 
      customErrorMessages: {
        'format': 'Enter a valid Date'
      });
  }

  // Validate all fields
  bool validateForm() {
    bool isValid = true;
    // Validate all fields
    isValid =
        _validateScore(memoryScoreController.text, memoryScoreError) && isValid;
    isValid = _validateProcessingSpeed(
            processingSpeedController.text, processingSpeedError) &&
        isValid;
    isValid =
        _validateReactionTime(reactionTimeController.text, reactionTimeError) &&
            isValid;
    isValid = _validateExecutiveFunction(
            executiveFunctionController.text, executiveFunctionError) &&
        isValid;
    isValid = _validateScore(
            visuospatialScoreController.text, visuospatialScoreError) &&
        isValid;
    isValid = _validateTestDate(testDateController.text) && isValid;

    return isValid;
  }

  Map<String, dynamic> getCognivueData() {
    return {
      'Memory': memoryScoreController.text,
      'ProcessingSpeed': processingSpeedController.text,
      'ReactionTime': reactionTimeController.text,
      'ExecutiveFunction': executiveFunctionController.text,
      'VisuoSpatial': visuospatialScoreController.text,
      'TestDate': testDateController.text,
    };
  }

  /// Prefills the form fields with extracted Cognivue data
  void prefillWithExtractedData(CognivueDetailsModel data) {
    debugPrint('🧠 COGNIVUE CONTROLLER: Received data from GeminiReportCard');
    // Reset the form before prefilling
    resetForm();

    if (data.memory.isNotEmpty) {
      debugPrint('🧠 COGNIVUE CONTROLLER: Setting memory score: ${data.memory}');
      memoryScoreController.text = data.memory;
    }
    if (data.visuoSpatial.isNotEmpty) {
      debugPrint('🧠 COGNIVUE CONTROLLER: Setting visuospatial score: ${data.visuoSpatial}');
      visuospatialScoreController.text = data.visuoSpatial;
    }
    if (data.executiveFunction.isNotEmpty) {
      debugPrint('🧠 COGNIVUE CONTROLLER: Setting executive function: ${data.executiveFunction}');
      executiveFunctionController.text = data.executiveFunction;
    }
    if (data.reactionTime.isNotEmpty) {
      debugPrint('🧠 COGNIVUE CONTROLLER: Setting reaction time: ${data.reactionTime}');
      reactionTimeController.text = data.reactionTime;
    }
    if (data.processingSpeed.isNotEmpty) {
      debugPrint('🧠 COGNIVUE CONTROLLER: Setting processing speed: ${data.processingSpeed}');
      processingSpeedController.text = data.processingSpeed;
    }
    if (data.testDate.isNotEmpty) {
      debugPrint('🧠 COGNIVUE CONTROLLER: Setting test date: ${data.testDate}');
      testDateController.text = data.testDate;
    }
    // Switch to edit mode after extraction
    isReadOnly.value = false;
    // Validate the form
    debugPrint('🧠 COGNIVUE CONTROLLER: Validating form after prefill');
    validateForm();
  }

  Future<void> postReport() async {
    try {
      isLoading.value = true;
      bool response = await _apiService.postDetails(
        type: BasicDetailTypes.Cognivue,
        detailsData: getCognivueData(),
      );
      if (response) {
        Get.offAllNamed(MetaView.routeName);
      } else {
        reusableSnackBar(message: "Something went wrong...");
      }
    } catch (e) {
      debugPrint("Error: $e");
      reusableSnackBar(message: "Something went wrong...");
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> onSaveAndContinue() async {
    if (validateForm()) {
      await postReport();
    } else {
      reusableSnackBar(message: "Please fill all required fields correctly");
    }
  }

  // Fetch saved cognivue data from API
  Future<void> fetchSavedCognivueData() async {
    try {
      isLoading.value = true;
      final Map<String, dynamic>? response = await _apiService.getDetails(
        type: BasicDetailTypes.Cognivue,
      );

      if (response != null) {
        final cognivueDetails = CognivueDetailsModel.fromJson(response);

        // Prefill form fields with the retrieved data
        memoryScoreController.text = cognivueDetails.memory;
        processingSpeedController.text = cognivueDetails.processingSpeed;
        reactionTimeController.text = cognivueDetails.reactionTime;
        executiveFunctionController.text = cognivueDetails.executiveFunction;
        visuospatialScoreController.text = cognivueDetails.visuoSpatial;
        testDateController.text = cognivueDetails.testDate.isNotEmpty
            ? cognivueDetails.testDate
            : DateFormat('MM/dd/yyyy').format(DateTime.now());
      }
    } catch (e) {
      debugPrint("Error fetching saved cognivue data: $e");
      // Set today's date as default if there's an error
      testDateController.text = DateFormat('MM/dd/yyyy').format(DateTime.now());
    } finally {
      isLoading.value = false;
    }
  }

  // Check completion status from MetaController
  Future<void> _checkCompletionStatus() async {
    try {
      final metaController = Get.find<MetaController>();
      final routeName = CognivueReportDataEntryView.routeName;
      final isCompleted = metaController.moduleCompletionStatus[routeName] ?? false;
      final hasCompletedGTPlusOnce = metaController.hasPatientCompletedGTPlusOnce();

      debugPrint("Cognivue route name: $routeName");
      debugPrint("Cognivue completion status: $isCompleted");
      debugPrint("Has patient completed GT+ once: $hasCompletedGTPlusOnce");
      debugPrint("All completion statuses: ${metaController.moduleCompletionStatus}");

      if (isCompleted || hasCompletedGTPlusOnce) {
        debugPrint("Setting cognivue form to read-only mode");
        enableReadOnlyMode();
      }
    } catch (e) {
      // MetaController might not be initialized yet, that's okay
      debugPrint("MetaController not found or not initialized: $e");
    }
  }
}
