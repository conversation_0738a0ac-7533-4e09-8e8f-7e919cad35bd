import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../utils/date_input_fomatter.dart';
import '../controller/cognivue_report_data_entry_controller.dart';

import '../../../utils/appConst/app_colors.dart';
import '../../../utils/reusableWidgets/custom_text_field_for_app.dart';
import '../../../utils/reusableWidgets/reusable_app_bar.dart';
import '../../../utils/reusableWidgets/reusable_button.dart';
import '../../../utils/reusableWidgets/reusable_footer.dart';
import '../../gemini_image_to_text/gemini_report_card.dart';
import '../../../models/cognivue_details_model.dart';
import '../../../utils/reusableWidgets/gemini_analysis_card.dart';
import '../../../utils/reusableWidgets/gemini_bottom_sheet.dart';

class CognivueReportDataEntryView
    extends GetView<CognivueReportDataEntryController> {
  const CognivueReportDataEntryView({super.key});

  static const String routeName = "/CognivueReportDataEntryView";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: reusableAppBar(context: context),
      bottomNavigationBar: const ReusableFooter(),
      body: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    return Obx(() {
      if (controller.isInitializing.value) {
        return const Center(child: CircularProgressIndicator());
      }

      return SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: context.width * .04),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: context.height * .04),
              _buildSectionTitle("Cognivue Report Data Entry", 24),
              SizedBox(height: context.height * .02),
              _buildReadOnlyIndicator(),
              _buildGeminiButton(context),
              const SizedBox(height: 32),
              _buildScoresSection(context),
              const SizedBox(height: 32),
              _buildTimesSection(context),
              const SizedBox(height: 32),
              _buildTestDateSection(context),
              const SizedBox(height: 32),
              _buildActionButtons(context),
              const SizedBox(height: 32),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildSectionTitle(String title, double fontSize) {
    return Text(
      title,
      style: TextStyle(
        color: AppColors.charcoalBlue,
        fontSize: fontSize,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildReadOnlyIndicator() {
    return Obx(() {
      if (controller.isReadOnly.value) {
        // Read-Only Mode Indicator
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            border: Border.all(color: Colors.blue.shade200),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                Icons.lock_outline,
                color: Colors.blue.shade600,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  "Read-Only Mode: This form has been completed. Use 'Edit' to make changes or 'Reset' to start over.",
                  style: TextStyle(
                    color: Colors.blue.shade700,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        );
      } else if (controller.isFormCompleted.value && !controller.isReadOnly.value) {
        // Edit Mode Indicator (for previously completed forms)
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.orange.shade50,
            border: Border.all(color: Colors.orange.shade200),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                Icons.edit_outlined,
                color: Colors.orange.shade600,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  "Edit Mode: You are now editing a previously completed form. Changes will be saved when you submit.",
                  style: TextStyle(
                    color: Colors.orange.shade700,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        );
      } else {
        return const SizedBox.shrink();
      }
    });
  }

  Widget _buildGeminiButton(BuildContext context) {
    return GeminiAnalysisCard(
      title: 'AI-Powered Cognivue Analysis',
      reportType: 'Cognivue',
      onTap: () => _showGeminiBottomSheet(context),
    );
  }

  /// Shows a bottom sheet with the Gemini Cognivue analysis tool
  void _showGeminiBottomSheet(BuildContext context) {
    showGeminiBottomSheet(
      context: context,
      title: 'AI Cognivue Analysis',
      reportType: ReportType.cognivue,
      toolTitle: 'Cognivue Report Scanner',
      description: 'Extract Cognivue Data with AI',
      onDataExtracted: (data) => controller.prefillWithExtractedData(data as CognivueDetailsModel),
    );
  }

  Widget _buildScoresSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle("Cognitive Performance Scores", 18),
        const SizedBox(height: 8),
        const Text(
          "(Scores range from 0-100, higher scores are better)",
          style: TextStyle(
            color: AppColors.charcoalBlue,
            fontSize: 14,
            fontStyle: FontStyle.italic,
          ),
        ),
        const SizedBox(height: 16),
        _buildScoresRow(context),
      ],
    );
  }

  Widget _buildScoresRow(BuildContext context) {
    return Obx(() => Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomTextFieldForApp(
              textEditingController: controller.memoryScoreController,
              hintText: "0-100",
              title: "Memory Score (%)",
              maxLength: 3,
              textInputType: TextInputType.number,
              errorText: controller.memoryScoreError.value,
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
            const SizedBox(width: 16),
            CustomTextFieldForApp(
              textEditingController: controller.visuospatialScoreController,
              hintText: "0-100",
              maxLength: 3,
              title: "Visuospatial Score (%)",
              textInputType: TextInputType.number,
              errorText: controller.visuospatialScoreError.value,
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
            const SizedBox(width: 16),
            Expanded(child: Container()),
          ],
        ));
  }

  Widget _buildTimesSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle("Processing Measurements", 18),
        const SizedBox(height: 8),
        const Text(
          "(Time measurements in milliseconds, lower is better for processing speed and reaction time)",
          style: TextStyle(
            color: AppColors.charcoalBlue,
            fontSize: 14,
            fontStyle: FontStyle.italic,
          ),
        ),
        const SizedBox(height: 16),
        _buildProcessingRow1(context),
        const SizedBox(height: 24),
        _buildProcessingRow2(context),
      ],
    );
  }

  Widget _buildProcessingRow1(BuildContext context) {
    return Obx(() => Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomTextFieldForApp(
              textEditingController: controller.processingSpeedController,
              hintText: "100-2500 ms",
              title: "Processing Speed (ms)",
              maxLength: 4,
              textInputType: TextInputType.number,
              errorText: controller.processingSpeedError.value,
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
            const SizedBox(width: 16),
            CustomTextFieldForApp(
              textEditingController: controller.reactionTimeController,
              hintText: "100-2000 ms",
              title: "Reaction Time (ms)",
              maxLength: 4,
              textInputType: TextInputType.number,
              errorText: controller.reactionTimeError.value,
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
            const SizedBox(width: 16),
            Expanded(child: Container()),
          ],
        ));
  }

  Widget _buildProcessingRow2(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Obx(
          () => CustomTextFieldForApp(
            textEditingController: controller.executiveFunctionController,
            hintText: "0-100",
            title: "Executive Function Score",
            textInputType: TextInputType.number,
            maxLength: 3,
            errorText: controller.executiveFunctionError.value,
            readOnly: controller.isReadOnly.value,
            onChanged: (value) {
              controller.validateForm();
            },
          ),
        ),
        const SizedBox(width: 16),
        Expanded(child: Container()),
        Expanded(child: Container()),
      ],
    );
  }

  Widget _buildTestDateSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle("Test Information", 18),
        const SizedBox(height: 16),
        _buildTestDateRow(context),
      ],
    );
  }

  Widget _buildTestDateRow(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Obx(
          () => CustomTextFieldForApp(
            textEditingController: controller.testDateController,
            hintText: "mm/dd/yyyy",
            inputFormatters: [DateInputFormatter()],
            title: "Test Date",
            textInputType: TextInputType.datetime,
            errorText: controller.testDateError.value,
            readOnly: controller.isReadOnly.value,
            onChanged: (value) {
              controller.validateForm();
            },
          ),
        ),
        const SizedBox(width: 16),
        Expanded(child: Container()),
        Expanded(child: Container()),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Obx(() {
      if (controller.isReadOnly.value) {
        // Show Reset and Edit buttons when in read-only mode
        return Row(
          children: [
            ReusableButton(
              width: 150,
              title: 'Reset',
              color: Colors.red,
              borderColor: Colors.red,
              onTap: () {
                controller.resetForm();
              },
            ),
            const SizedBox(width: 16),
            ReusableButton(
              width: 150,
              title: 'Edit',
              onTap: () {
                controller.enableEditMode();
              },
            ),
          ],
        );
      } else {
        // Show Submit button when in edit mode
        return ReusableButton(
          width: 150,
          title: 'Submit',
          isLoading: controller.isLoading.value,
          onTap: controller.onSaveAndContinue,
        );
      }
    });
  }
}
