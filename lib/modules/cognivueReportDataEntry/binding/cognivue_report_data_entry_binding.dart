import 'package:get/get.dart';
import 'package:gt_plus/modules/cognivueReportDataEntry/controller/cognivue_report_data_entry_controller.dart';
import '../../../services/gemini_service.dart';

class CognivueReportDataEntryBinding {
  static List<Bindings> binding = [
    BindingsBuilder(() {
      // Ensure the controller is put
      Get.put(CognivueReportDataEntryController());
      
      // Ensure GeminiService is initialized and available for AI data extraction
      if (!Get.isRegistered<GeminiService>()) {
        Get.putAsync(() => GeminiService().init());
      }
    }),
  ];
}
