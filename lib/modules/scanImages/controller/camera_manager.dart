import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gt_plus/services/remoteConfig/firebase_remote_config_service.dart';
import 'package:gt_plus/services/sound_service.dart';
import 'package:native_device_orientation/native_device_orientation.dart';

class CameraManager {
  CameraController? cameraController;
  final FirebaseRemoteConfigService _remoteConfigService;
  final SoundService _soundService = SoundService();
  bool isFlashNeededFromConfig = false;
  bool isFlashActive = false;

  CameraManager({required FirebaseRemoteConfigService remoteConfigService})
      : _remoteConfigService = remoteConfigService {
    isFlashNeededFromConfig = _remoteConfigService.isFlashNeeded();
  }

  Future<void> initializeCamera({
    required bool isFrontCamera,
    required Function(BuildContext) addListenerCallback,
    required BuildContext context,
  }) async {
    try {
      final cameras = await availableCameras();
      if (cameras.isEmpty) return;

      if (cameraController != null) {
        await cameraController!.dispose();
        cameraController = null;
      }

      final camera = isFrontCamera
          ? cameras.firstWhere(
              (camera) => camera.lensDirection == CameraLensDirection.front,
              orElse: () => cameras.first)
          : cameras.firstWhere(
              (camera) => camera.lensDirection == CameraLensDirection.back,
              orElse: () => cameras.first);

      cameraController = CameraController(
        camera,
        ResolutionPreset.high,
        enableAudio: false,
      );
      await cameraController!.initialize();

      if (cameraController!.value.isInitialized) {
        try {
          // Use auto focus for back camera, locked for front camera
          final isBackCamera = camera.lensDirection == CameraLensDirection.back;
          if (isBackCamera) {
            await cameraController!.setFocusMode(FocusMode.auto);
            debugPrint('Set initial focus mode to auto for back camera');
          } else {
            await cameraController!.setFocusMode(FocusMode.locked);
            debugPrint('Set initial focus mode to locked for front camera');
          }
          
          await cameraController!.setExposureMode(ExposureMode.auto);
          await setCameraFlash();
        } catch (e) {
          debugPrint('Error setting camera parameters: $e');
        }
      }

      addListenerCallback(context);
      DeviceOrientation lockOrientation = getDeviceOrientation(context);
      await cameraController?.lockCaptureOrientation(lockOrientation);
    } catch (e) {
      debugPrint('Error initializing camera: $e');
    }
  }

  DeviceOrientation getDeviceOrientation(BuildContext? context) {
    DeviceOrientation defaultOrientation = DeviceOrientation.landscapeLeft;

    if (context == null) {
      debugPrint('Context is null in getDeviceOrientation');
      return defaultOrientation;
    }

    try {
      Orientation? queryOrientation;
      try {
        queryOrientation = MediaQuery.of(context).orientation;
      } catch (e) {
        debugPrint('MediaQuery error: $e');
      }
      try {
        final nativeOrientation =
            NativeDeviceOrientationReader.orientation(context);

        switch (nativeOrientation) {
          case NativeDeviceOrientation.portraitUp:
            return DeviceOrientation.portraitUp;
          case NativeDeviceOrientation.portraitDown:
            return DeviceOrientation.portraitDown;
          case NativeDeviceOrientation.landscapeLeft:
            return DeviceOrientation.landscapeLeft;
          case NativeDeviceOrientation.landscapeRight:
            return DeviceOrientation.landscapeRight;
          case NativeDeviceOrientation.unknown:
            break;
        }
      } catch (e) {
        debugPrint('Native orientation error: $e');
      }

      if (queryOrientation != null) {
        return queryOrientation == Orientation.portrait
            ? DeviceOrientation.portraitUp
            : DeviceOrientation.landscapeLeft;
      }

      return defaultOrientation;
    } catch (e) {
      debugPrint('Error in getDeviceOrientation: $e');
      return defaultOrientation;
    }
  }

  Future<void> setCameraFlash() async {
    isFlashNeededFromConfig = _remoteConfigService.isFlashNeeded();
    debugPrint('Remote config flash setting: $isFlashNeededFromConfig');

    if (cameraController == null || !cameraController!.value.isInitialized) {
      debugPrint('Camera not initialized, cannot set flash');
      return;
    }

    if (!isFlashNeededFromConfig) {
      try {
        await cameraController!.setFlashMode(FlashMode.off);
        isFlashActive = false;
        debugPrint('Flash disabled by remote config');
      } catch (e) {
        debugPrint('Error disabling flash: $e');
      }
      return;
    }

    // Only try the always flash mode
    try {
      await cameraController!.setFlashMode(FlashMode.always);
      debugPrint('Flash set to always mode successfully');
      isFlashActive = true;
    } catch (e) {
      debugPrint('Failed to set flash mode: $e');
      isFlashActive = false;
    }
  }

  Future<XFile?> takePicture() async {
    if (cameraController == null || !cameraController!.value.isInitialized) {
      return null;
    }
    try {
      // Play shutter sound before taking picture
      await _soundService.playShutterSound();

      final XFile photo = await cameraController!.takePicture();
      return photo;
    } catch (e) {
      debugPrint('Camera Error: $e');
      return null;
    }
  }

  Future<void> setFocusPoint(
      Offset normalizedPosition, Function(bool) focusCallback) async {
    if (cameraController == null || !cameraController!.value.isInitialized) {
      debugPrint('Cannot set focus: Camera not ready');
      return;
    }

    try {
      focusCallback(true); // Start focusing

      // Clamp values between 0 and 1
      final clampedPosition = Offset(normalizedPosition.dx.clamp(0.0, 1.0),
          normalizedPosition.dy.clamp(0.0, 1.0));

      // Set focus mode to auto first
      try {
        await cameraController!.setFocusMode(FocusMode.auto);
        
        // Give time for focus mode to change
        await Future.delayed(const Duration(milliseconds: 200));
        
        // Set focus point
        try {
          await cameraController!.setFocusPoint(clampedPosition);
          debugPrint('Focus point set successfully');
        } catch (e) {
          debugPrint('Setting specific focus point not supported: $e');
        }
        
        // Allow camera to focus for a longer time
        await Future.delayed(const Duration(milliseconds: 1000));
        
        // Set exposure point
        try {
          await cameraController!.setExposurePoint(clampedPosition);
          debugPrint('Exposure point set successfully');
        } catch (e) {
          debugPrint('Error setting exposure point: $e');
        }
        
        // Keep focus mode auto for better continuous focus
        // Only lock focus if using front camera (which often doesn't support continuous focus)
        final isUsingFrontCamera = cameraController!.description.lensDirection == CameraLensDirection.front;
        if (isUsingFrontCamera) {
          await cameraController!.setFocusMode(FocusMode.locked);
          debugPrint('Focus locked (front camera)');
        } else {
          debugPrint('Keeping auto focus mode for back camera');
        }
        
        // Hide focus indicator after a delay
        Future.delayed(const Duration(seconds: 2), () {
          focusCallback(false);
          debugPrint('Focus indicator hidden');
        });
      } catch (e) {
        debugPrint('Error with focus operations: $e');
        // Try to recover by setting focus mode back to auto
        try {
          await cameraController!.setFocusMode(FocusMode.auto);
        } catch (_) {}
        focusCallback(false);
      }
    } catch (e) {
      debugPrint('Error in setFocusPoint: $e');
      focusCallback(false);
    }
  }

  void dispose() {
    try {
      if (cameraController != null) {
        // First check if the controller is already disposed
        if (!cameraController!.value.isInitialized) {
          cameraController = null;
          return;
        }
        
        // First stop image stream if it's running
        try {
          if (cameraController!.value.isStreamingImages) {
            cameraController!.stopImageStream();
          }
        } catch (e) {
          debugPrint('Error stopping image stream: $e');
        }
        
        // Then dispose the controller in a try-catch to handle exceptions
        try {
          cameraController!.dispose();
        } catch (e) {
          debugPrint('Error during camera disposal: $e');
        } finally {
          cameraController = null;
          debugPrint('Camera controller set to null');
        }
      }
    } catch (e) {
      debugPrint('Error disposing camera controller: $e');
      // Ensure controller is nullified even if an error occurs
      cameraController = null;
    }
  }
}
