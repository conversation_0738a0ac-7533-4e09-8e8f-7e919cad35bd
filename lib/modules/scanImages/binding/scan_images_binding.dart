import 'package:get/get.dart';
import 'package:gt_plus/modules/meta/controller/meta_controller.dart';
import 'package:gt_plus/modules/scanImages/controller/face_mesh_controller.dart';
import 'package:gt_plus/modules/scanImages/controller/scan_images_controller.dart';
import '../../BleFlutter/ble_flutter_controller.dart';


class ScanImagesBinding {
  static List<Bindings> binding = [
    BindingsBuilder.put(() => MetaController()),
    BindingsBuilder.put(() => ScanImagesController()),
  ];
}

class FaceMeshBinding {
  static List<Bindings> binding = [
    BindingsBuilder.put(() => FaceMeshController()),
  ];
}
