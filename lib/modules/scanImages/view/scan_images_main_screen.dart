import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gt_plus/modules/scanImages/controller/scan_images_controller.dart';

import '../../../utils/appConst/app_colors.dart';
import '../../../utils/reusableWidgets/reusable_app_bar.dart';
import '../../../utils/reusableWidgets/reusable_button.dart';
import '../../../utils/reusableWidgets/reusable_footer.dart';
import '../../../utils/reusableWidgets/reusable_scan_images_main_box.dart';

class ScanImagesMainView extends GetView<ScanImagesController> {
  const ScanImagesMainView({super.key});

  static const String routeName = "/ScanImagesMainView";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: reusableAppBar(
        context: context,
        onBackPressed: () {
          controller.onDoneClick();
        },
        backIcon: Icons.arrow_back_ios_new,
      ),
      bottomNavigationBar: const ReusableFooter(),
      body: SingleChildScrollView(
        child: Padding(
            padding: EdgeInsets.symmetric(horizontal: context.width * .075),
            child: Obx(() => controller.isLoading.value
                ? const Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Center(
                        child: CircularProgressIndicator(),
                      ),
                    ],
                  )
                : Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        height: context.height * .04,
                      ),
                      const Text(
                        "Images",
                        style: TextStyle(
                          color: AppColors.charcoalBlue,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(
                        height: context.height * .02,
                      ),
                      // Dynamically generate rows based on scanImageDataList length
                      ...List.generate(
                        (controller.scanImageDataList.value.length + 2) ~/ 3,
                        (rowIndex) {
                          final startIndex = rowIndex * 3;
                          final endIndex = (startIndex + 3 >
                                  controller.scanImageDataList.value.length)
                              ? controller.scanImageDataList.value.length
                              : startIndex + 3;

                          return Column(
                            children: [
                              Row(
                                children: List.generate(
                                  endIndex - startIndex,
                                  (index) =>
                                      Obx(() => ReusableScanImagesMainBox(
                                            scanImageDataList: controller
                                                .scanImageDataList.value,
                                            index: startIndex + index,
                                          )),
                                ),
                              ),
                              if (rowIndex <
                                  (controller.scanImageDataList.value.length +
                                              2) ~/
                                          3 -
                                      1)
                                SizedBox(
                                  height: context.height * .035,
                                ),
                            ],
                          );
                        },
                      ),
                      const SizedBox(
                        height: 32,
                      ),
                      ReusableButton(
                        width: 140,
                        title: 'Submit',
                        onTap: () {
                          controller.onDoneClick();
                        },
                      ),
                    ],
                  ))),
      ),
    );
  }
}
