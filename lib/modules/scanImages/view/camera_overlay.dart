import 'package:flutter/material.dart';

class CameraOverlay extends StatelessWidget {
  const CameraOverlay({
    super.key,
    required this.previewWidth,
    required this.previewHeight,
    required this.overlayColour,
    this.borderColor = Colors.white,
    this.cornerRadius = 20.0,
  });

  final double previewWidth;
  final double previewHeight;
  final Color overlayColour;
  final Color borderColor;
  final double cornerRadius;

  @override
  Widget build(BuildContext context) {
    bool isPortrait = previewHeight > previewWidth;
    double scanAreaSize = (isPortrait ? previewWidth : previewHeight) * 0.75;

    return Container(
      width: previewWidth,
      height: previewHeight,
      child: Stack(
        fit: StackFit.expand,
        children: [
          ColorFiltered(
            colorFilter: ColorFilter.mode(overlayColour, BlendMode.srcOut),
            child: Stack(
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: overlayColour,
                  ),
                ),
                Center(
                  child: Container(
                    width: scanAreaSize,
                    height: scanAreaSize,
                    decoration: BoxDecoration(
                      color: Colors.black,
                      borderRadius: BorderRadius.circular(cornerRadius),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Center(
            child: Container(
              width: scanAreaSize,
              height: scanAreaSize,
              decoration: BoxDecoration(
                border: Border.all(color: borderColor, width: 2.0),
                borderRadius: BorderRadius.circular(cornerRadius),
              ),
            ),
          ),
          // Add corner guidelines and other elements as needed
        ],
      ),
    );
  }
}
