import 'package:arkit_plugin/arkit_plugin.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gt_plus/modules/scanImages/controller/face_mesh_controller.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_button.dart';

class FaceMeshView extends GetView<FaceMeshController> {
  const FaceMeshView({super.key});
  static const routeName = "/FaceMeshView";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Face Detection')),
      body: FutureBuilder<bool>(
        future: controller.isARKitSupported(),
        builder: (BuildContext context, AsyncSnapshot<bool> snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          final bool isSupported = snapshot.data ?? false;

          if (!isSupported) {
            return _buildUnsupportedWidget();
          }

          return Obx(() {
            if (controller.showInstructions.value) {
              return _buildInstructionsPopup();
            } else {
              return _buildARViewWithHints();
            }
          });
        },
      ),
    );
  }

  Widget _buildARViewWithHints() {
    return Stack(
      children: [
        ARKitSceneView(
          configuration: ARKitConfiguration.faceTracking,
          onARKitViewCreated: controller.onARKitViewCreated,
        ),
        Obx(() => controller.isProcessing.value
            ? const Center(child: CircularProgressIndicator())
            : const SizedBox()),
        _buildHintsOverlay(),
      ],
    );
  }

  Widget _buildInstructionsPopup() {
    return Container(
      color: Colors.black.withOpacity(0.9),
      child: Center(
        child: Container(
          padding: const EdgeInsets.all(20),
          margin: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Instructions',
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'Hand the iPad to the patient. Before you begin:\n• The patient should keep a neutral expression.\n• Please remove any glasses, hats or face coverings.\n• Ensure your face is well-lit.\n• Maintain a neutral facial expression.\n• Tap "Continue."',
                textAlign: TextAlign.left,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.black87,
                  height: 1.4,
                ),
              ),
              const SizedBox(height: 24),
              ReusableButton(
                width: 128,
                height: 45,
                title: "Continue",
                onTap: controller.onContinuePressed,
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUnsupportedWidget() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            const Text(
              'Device Not Supported',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              'ARKit face tracking requires an iOS device with A9 or later processor (iPhone 6s or newer) running iOS 11+.',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            OutlinedButton(
              onPressed: () => Get.back(),
              child: const Text('Go Back'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHintsOverlay() {
    return Positioned(
      bottom: 100,
      left: 0,
      right: 0,
      child: Obx(() => AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            child: Text(
              controller.currentHint.value,
              key: UniqueKey(),
              textAlign: TextAlign.center,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
                shadows: [
                  Shadow(
                    blurRadius: 4.0,
                    color: Colors.black,
                    offset: Offset(2.0, 2.0),
                  ),
                ],
              ),
            ),
          )),
    );
  }
}
