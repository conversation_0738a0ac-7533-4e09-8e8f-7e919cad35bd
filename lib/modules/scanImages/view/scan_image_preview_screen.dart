import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:gt_plus/modules/scanImages/controller/scan_images_controller.dart';
import '../../../utils/appConst/app_colors.dart';
import '../../../utils/reusableWidgets/reusable_button.dart';

class ScanImagePreviewScreen extends GetView<ScanImagesController> {
  const ScanImagePreviewScreen({super.key});

  static const routeName = "/ScanImagePreviewScreen";

  @override
  Widget build(BuildContext context) {
    final devicePadding = MediaQuery.of(context).padding;

    final bottomPadding =
        devicePadding.bottom > 0 ? devicePadding.bottom + 16 : 36;

    // Set safe area top padding for notched devices
    final topPadding = devicePadding.top > 0 ? devicePadding.top : 16;

    return Scaffold(
      body: SafeArea(
        child: Obx(
          () => controller.isLoading.value
              ? const Center(
                  child: CircularProgressIndicator(
                    color: AppColors.orange,
                  ),
                )
              : OrientationBuilder(
                  builder: (context, orientation) {
                    return orientation == Orientation.portrait
                        ? _buildPortraitLayout(context,
                            bottomPadding.toDouble(), topPadding.toDouble())
                        : _buildLandscapeLayout(
                            context, bottomPadding.toDouble());
                  },
                ),
        ),
      ),
    );
  }

  Widget _buildPortraitLayout(
      BuildContext context, double bottomPadding, double topPadding) {
    return Column(
      children: [
        // Optional custom app bar
        Container(
          padding:
              EdgeInsets.only(top: topPadding, left: 16, right: 16, bottom: 8),
          alignment: Alignment.centerLeft,
          child: const Text(
            "Preview",
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),

        // Image container with flexible height
        Expanded(
          child: Obx(() => Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Image.file(
                    File(controller.imagePath.value),
                    fit: BoxFit.contain,
                  ),
                ),
              )),
        ),

        // Button row
        _buildButtonRow(context, bottomPadding),
      ],
    );
  }

  Widget _buildLandscapeLayout(BuildContext context, double bottomPadding) {
    return Row(
      children: [
        // Image container
        Expanded(
          flex: 3,
          child: Obx(() => Container(
                padding: const EdgeInsets.all(16),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Image.file(
                    File(controller.imagePath.value),
                    fit: BoxFit.contain,
                  ),
                ),
              )),
        ),

        // Button column on the right
        Expanded(
          flex: 1,
          child: Padding(
            padding: EdgeInsets.only(right: 16, bottom: bottomPadding),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ReusableButton(
                  title: "Retake",
                  fontColor: AppColors.orange,
                  color: Colors.white,
                  onTap: () {
                    // Initialize camera before going back
                    controller.reinitializeCamera(context).then((_) {
                      Get.back();
                    });
                  },
                ),
                const SizedBox(height: 16),
                Obx(() => ReusableButton(
                      title: "Done",
                      isLoading: controller.isDoneButtonLoading.value,
                      onTap: () {
                        controller.uploadPhoto(context);
                      },
                    )),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildButtonRow(BuildContext context, double bottomPadding) {
    return Padding(
      padding:
          EdgeInsets.only(left: 16, right: 16, bottom: bottomPadding, top: 16),
      child: Row(
        children: [
          Expanded(
            child: ReusableButton(
              title: "Retake",
              fontColor: AppColors.orange,
              color: Colors.white,
              onTap: () {
                // Initialize camera before going back
                controller.reinitializeCamera(context).then((_) {
                  Get.back();
                });
              },
            ),
          ),
          const SizedBox(width: 24),
          Expanded(
            child: Obx(() => ReusableButton(
                  title: "Done",
                  isLoading: controller.isLoading.value,
                  onTap: () {
                    controller.uploadPhoto(context);
                  },
                )),
          ),
        ],
      ),
    );
  }
}
