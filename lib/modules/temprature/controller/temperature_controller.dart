import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:get/get.dart';
import 'package:gt_plus/global_controller.dart';
import 'package:gt_plus/modules/temprature/view/temperature_reading_view.dart';
import 'package:gt_plus/services/api_service.dart';
import '../../../services/prefs_service.dart';
import '../../../utils/reusableWidgets/resusable_snackbar.dart';
import '../../BleFlutter/ble_flutter_controller.dart';
import '../../meta/view/meta_view.dart';

class TemperatureController extends GetxController {
  RxBool isLoading = false.obs;
  final BleFlutterController bleFlutterController =
      Get.find<BleFlutterController>();
  final PrefsService prefs = PrefsService();
  final ApiService _apiService = ApiService();
  TextEditingController tempFhController = TextEditingController();
  TextEditingController tempBteController = TextEditingController();

  final GlobalController globalController = Get.find<GlobalController>();

  // Error message strings
  RxString tempFhError = ''.obs;
  RxString tempBteError = ''.obs;
  Rx<DeviceStatus> deviceStatus = DeviceStatus.disconnected.obs;

  Worker? _temperatureListener;
  Worker? _connectedDevicesListener;

  @override
  void onInit() {
    super.onInit();
    _checkSavedDevice();
  }

  void _setupBleListeners() {
    _temperatureListener?.dispose();
    _connectedDevicesListener?.dispose();

    _temperatureListener = ever(bleFlutterController.temperature, (value) {
      if (tempBteController.text.isEmpty) {
        tempBteController.text = value.toStringAsFixed(1);
      } else if (tempFhController.text.isEmpty) {
        tempFhController.text = value.toStringAsFixed(1);
      }
    });

    _connectedDevicesListener = ever(
      bleFlutterController.connectedDevices,
      (Map<BleDevice, BluetoothDevice?> devices) {
        if (devices[BleDevice.temp] != null) {
          deviceStatus.value = DeviceStatus.connected;
        } else {
          deviceStatus.value = DeviceStatus.disconnected;
        }

        if (Get.currentRoute == TemperatureReadingView.routeName) {
          if (devices[BleDevice.temp] == null &&
              (tempBteController.text.isEmpty ||
                  tempFhController.text.isEmpty)) {
            bleFlutterController.showDeviceSelectionPopup(BleDevice.temp);
          }
        }
      },
    );
  }

  void _checkSavedDevice() async {
    deviceStatus.value = DeviceStatus.connecting;
    String savedId = await prefs.getTempRemoteId();
    if (savedId.isNotEmpty) {
      BluetoothDevice device =
          BluetoothDevice(remoteId: DeviceIdentifier(savedId));
      bool isConnected = await bleFlutterController.connect(device);
      if (!isConnected) {
        deviceStatus.value = DeviceStatus.disconnected;
        bleFlutterController.showDeviceSelectionPopup(BleDevice.temp);
      }
    } else {
      deviceStatus.value = DeviceStatus.disconnected;
      bleFlutterController.showDeviceSelectionPopup(BleDevice.temp);
    }
    _setupBleListeners();
  }

  double? _parseDoubleValue(String value) {
    if (value.isEmpty) return null;
    try {
      return double.parse(value);
    } catch (e) {
      return null;
    }
  }

  void resetFrom() {
    tempBteController.clear();
    tempFhController.clear();

    tempFhError.value = '';
    tempBteError.value = '';
    isLoading.value = false;

    _checkSavedDevice();
  }

  bool validateForm() {
    bool isValid = true;

    isValid =
        _validateFahrenheit(tempFhController.text, tempFhError) && isValid;
    isValid =
        _validateFahrenheit(tempBteController.text, tempBteError) && isValid;

    return isValid;
  }

  // Validate Fahrenheit temperature
  bool _validateFahrenheit(String value, RxString errorMsg) {
    errorMsg.value = '';

    if (value.isEmpty) {
      errorMsg.value = "Please enter temperature value";
      return false;
    }

    RegExp regExp = RegExp(r'^\d+(\.\d+)?$');
    if (!regExp.hasMatch(value)) {
      errorMsg.value = "Temperature must be a valid number";
      return false;
    }

    double? temp = _parseDoubleValue(value);
    if (temp == null) {
      errorMsg.value = "Please enter valid temperature";
      return false;
    }

    // Validate temperature range (80-120°F)
    if (temp < 80 || temp > 120) {
      errorMsg.value = "Temperature must be between 80°F and 120°F";
      return false;
    }

    return true;
  }

  Future<void> onSubmitClick() async {
    if (!validateForm()) {
      return;
    }
    double tempFh = double.parse(tempFhController.text);
    double tempBte = double.parse(tempBteController.text);
    await postPpg(tempBte: tempBte, tempFh: tempFh);
  }

  Future<void> postPpg(
      {required double tempBte, required double tempFh}) async {
    try {
      isLoading.value = true;
      bool response =
          await _apiService.postTemperature(tempBte: tempBte, tempFh: tempFh);

      if (response) {
        Get.offAllNamed(MetaView.routeName);
      } else {
        reusableSnackBar(message: "Something went wrong...");
      }
    } catch (e) {
      debugPrint("Error: $e");
      reusableSnackBar(message: "Something went wrong...");
    } finally {
      isLoading.value = false;
    }
  }
}
