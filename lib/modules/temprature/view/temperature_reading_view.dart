import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gt_plus/modules/temprature/controller/temperature_controller.dart';
import 'package:gt_plus/utils/reusableWidgets/custom_text_field_for_app.dart';

import '../../../utils/appConst/app_colors.dart';
import '../../../utils/appConst/app_images.dart';
import '../../../utils/reusableWidgets/reusable_app_bar.dart';
import '../../../utils/reusableWidgets/reusable_button.dart';
import '../../../utils/reusableWidgets/reusable_footer.dart';

class TemperatureReadingView extends GetView<TemperatureController> {
  const TemperatureReadingView({super.key});

  static const String routeName = "/TemperatureReadingView";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: reusableAppBar(
        context: context,
      ),
      bottomNavigationBar: const ReusableFooter(),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: context.width * .04),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: context.height * .04,
              ),
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: AppColors.lightTeal,
                  borderRadius: BorderRadius.circular(5),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      flex: 2,
                      child: Padding(
                        padding: EdgeInsets.only(left: context.width * .02),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              "Temperature Measurement",
                              style: TextStyle(
                                color: AppColors.white,
                                fontSize: 32,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(
                              height: 4,
                            ),
                            ...?controller
                                .globalController.instructions.temperature
                                ?.asMap()
                                .entries
                                .map((entry) {
                              final index = entry.key;
                              final point = entry.value;
                              return Padding(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 4.0),
                                child: Text(
                                  '${index + 1}. $point',
                                  style: const TextStyle(
                                    color: Colors.white,
                                  ),
                                ),
                              );
                            }),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(
                      width: 32,
                    ),
                    Expanded(
                      child: Image.asset(
                        AppImages.imgTemp,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: context.height * .03,
              ),
              Row(
                children: [
                  const Text(
                    "Temperature Readings",
                    style: TextStyle(
                      color: AppColors.charcoalBlue,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(
                    width: 16,
                  ),
                  Obx(
                    () => Text(
                      controller.deviceStatus.value.text,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: controller.deviceStatus.value.color,
                      ),
                    ),
                  )
                ],
              ),
              SizedBox(
                height: context.height * .02,
              ),
              SizedBox(
                height: context.height * .03,
              ),
              Obx(() => Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CustomTextFieldForApp(
                        title: "Temperature Behind The Ear (°F)",
                        textInputType: const TextInputType.numberWithOptions(
                            decimal: true, signed: true),
                        hintText: "Enter value",
                        //     inputFormatters: [TemperatureInputFormatter()],
                        errorText: controller.tempBteError.value,
                        textEditingController: controller.tempBteController,
                        onChanged: (value) {
                          controller.validateForm();
                        },
                      ),
                      const SizedBox(
                        width: 24,
                      ),
                      CustomTextFieldForApp(
                        title: "Temperature Forehead (°F)",
                        textInputType: const TextInputType.numberWithOptions(
                            decimal: true, signed: true),
                        hintText: "Enter value",
                        //   inputFormatters: [TemperatureInputFormatter()],
                        errorText: controller.tempFhError.value,
                        textEditingController: controller.tempFhController,
                        onChanged: (value) {
                          controller.validateForm();
                        },
                      ),
                    ],
                  )),
              const SizedBox(
                height: 32,
              ),
              Row(
                children: [
                  Obx(
                    () => ReusableButton(
                      width: context.width * .22,
                      isLoading: controller.isLoading.value,
                      title: 'Submit',
                      onTap: () {
                        controller.onSubmitClick();
                      },
                    ),
                  ),
                  const SizedBox(
                    width: 16,
                  ),
                  ReusableButton(
                    width: context.width * .20,
                    color: Colors.red,
                    borderColor: Colors.red,
                    title: 'Reset',
                    onTap: () {
                      controller.resetFrom();
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
