import 'dart:math';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:native_device_orientation/native_device_orientation.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_app_bar.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:gt_plus/modules/Qr/controller/qr_controller.dart';

class QrView extends GetView<QrController> {
  const QrView({super.key});

  static const routeName = "/QrView";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: reusableAppBar(
        context: context,
        actions: [],
      ),
      body: Obx(() => controller.showManualInput.value
          ? _buildManualInputView()
          : _buildQrScannerView(context)),
    );
  }

  Widget _buildManualInputView() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Text(
            'Enter Clinic Code',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 6),
          const Text(
            'Enter the string that is present in your clinic admin dashboard to register your GT +.',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w400,
            ),
          ),
          const SizedBox(height: 28),
          TextField(
            controller: controller.manualInputController,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              labelText: 'e.g. YourClinicName_Audiology_Id',
              hintText: 'e.g. YourClinicName_Audiology_Id',
            ),
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              OutlinedButton.icon(
                onPressed: controller.toggleManualInput,
                icon: const Icon(Icons.qr_code_scanner),
                label: const Text('Scan QR'),
              ),
              const SizedBox(width: 16),
              ElevatedButton.icon(
                onPressed: controller.submitManualInput,
                icon: const Icon(Icons.check),
                label: const Text('Submit'),
              ),
            ],
          ),
          Obx(() => controller.isLoading.value
              ? const Padding(
                  padding: EdgeInsets.only(top: 20),
                  child: CircularProgressIndicator(),
                )
              : const SizedBox.shrink()),
        ],
      ),
    );
  }

  Widget _buildQrScannerView(BuildContext context) {
    return StreamBuilder<NativeDeviceOrientation>(
      stream: NativeDeviceOrientationCommunicator()
          .onOrientationChanged(useSensor: true),
      builder: (context, snapshot) {
        final orientation = snapshot.data ?? NativeDeviceOrientation.portraitUp;
        // Calculate rotation angle based on orientation
        double rotationAngle;
        switch (orientation) {
          case NativeDeviceOrientation.landscapeLeft:
            rotationAngle = -pi / 2;
            break;
          case NativeDeviceOrientation.landscapeRight:
            rotationAngle = pi / 2;
            break;
          case NativeDeviceOrientation.portraitDown:
            rotationAngle = pi;
            break;
          default: // portraitUp
            rotationAngle = 0;
            break;
        }

        return Stack(
          fit: StackFit.expand,
          children: [
            Center(
              child: Transform.rotate(
                angle: rotationAngle,
                child: AspectRatio(
                  aspectRatio: 1.0,
                  child: MobileScanner(
                    controller: controller.scannerController,
                    onDetect: (capture) {
                      final List<Barcode> barcodes = capture.barcodes;
                      for (final barcode in barcodes) {
                        debugPrint('Barcode found: ${barcode.rawValue}');
                        controller.scanQrCode(barcode.rawValue ?? '');
                      }
                    },
                  ),
                ),
              ),
            ),

            // Scanner Overlay
            Positioned.fill(
              child: Obx(() => Container(
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: controller.scannerOverlayColor.value,
                        width: 4,
                      ),
                    ),
                  )),
            ),

            // Scanning Instructions
            Positioned(
              bottom: 20,
              left: 0,
              right: 0,
              child: Column(
                children: [
                  Center(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 10),
                      decoration: BoxDecoration(
                        color: Colors.black54,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: const Text(
                        'Scan the QR code present in the\nclinic admin dashboard to register the GT plus',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 10),
                  TextButton.icon(
                    onPressed: controller.toggleManualInput,
                    icon: const Icon(Icons.keyboard, color: Colors.white),
                    label: const Text(
                      'Register manually',
                      style: TextStyle(color: Colors.white),
                    ),
                    style: TextButton.styleFrom(
                      backgroundColor: Colors.black54,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Loading Indicator
            Obx(
              () => controller.isLoading.value
                  ? const Center(
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation(Colors.white),
                      ),
                    )
                  : const SizedBox.shrink(),
            ),
          ],
        );
      },
    );
  }
}
