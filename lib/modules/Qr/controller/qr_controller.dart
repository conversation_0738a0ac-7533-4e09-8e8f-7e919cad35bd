import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:gt_plus/services/api_service.dart';
import 'package:gt_plus/services/prefs_service.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import '../../../models/clinic_details_model.dart';
import '../../login/view/login_phone_view.dart';

class QrController extends GetxController {
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;
  final Rx<Color> scannerOverlayColor = Colors.white.withOpacity(0.3).obs;
  final RxBool showManualInput = false.obs;
  final TextEditingController manualInputController = TextEditingController();

  PrefsService prefsService = PrefsService();
  final ApiService _apiService = ApiService();

  final MobileScannerController scannerController = MobileScannerController(
    detectionSpeed: DetectionSpeed.noDuplicates,
    facing: CameraFacing.back,
    torchEnabled: false,
  );

  @override
  void onInit() {
    assert(() {
      getClinicDetails("SFAUDIOLOGY_Audiology");
      return true;
    }());
    super.onInit();
  }

  @override
  void onClose() {
    scannerController.dispose();
    manualInputController.dispose();
    super.onClose();
  }

  Future<void> scanQrCode(String qrValue) async {
    if (qrValue.isEmpty) {
      errorMessage.value = 'Invalid QR code';
      return;
    }
    await getClinicDetails(qrValue);
  }

  void toggleManualInput() {
    showManualInput.value = !showManualInput.value;
  }

  Future<void> submitManualInput() async {
    final inputValue = manualInputController.text.trim();
    if (inputValue.isEmpty) {
      errorMessage.value = 'Please enter a clinic code';
      _showErrorDialog();
      return;
    }

    await scannerController.stop();
    await getClinicDetails(inputValue);
  }

  Future<void> getClinicDetails(String qrValue) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      ClinicDetailsModel? clinicDetailsModel =
          await _apiService.getClinicDetails(qrValue);

      if (clinicDetailsModel != null) {
        prefsService.setClinicDetails(clinicDetailsModel);
        await scannerController.stop();
        await prefsService.setQrValue(qrValue: qrValue);
        Get.offAllNamed(LoginPhoneView.routeName);
      } else {
        errorMessage.value = 'Registration failed. Please try again.';
        _showErrorDialog();
      }
    } catch (e) {
      errorMessage.value = 'An error occurred. Please check your connection.';
      _showErrorDialog();
    } finally {
      isLoading.value = false;
    }
  }

  void _showErrorDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('Registration Error'),
        content: Obx(
          () => Text(
            errorMessage.value,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
