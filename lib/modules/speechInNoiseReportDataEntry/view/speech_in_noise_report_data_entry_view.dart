import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../utils/date_input_fomatter.dart';
import '../controller/speech_in_noise_report_data_entry_controller.dart';

import '../../../utils/appConst/app_colors.dart';
import '../../../utils/reusableWidgets/custom_text_field_for_app.dart';
import '../../../utils/reusableWidgets/reusable_app_bar.dart';
import '../../../utils/reusableWidgets/reusable_button.dart';
import '../../../utils/reusableWidgets/reusable_footer.dart';
import '../../gemini_image_to_text/gemini_report_card.dart';
import '../../../models/speech_in_noise_details_model.dart';

class SpeechInNoiseReportDataEntryView
    extends GetView<SpeechInNoiseReportDataEntryController> {
  const SpeechInNoiseReportDataEntryView({super.key});

  static const String routeName = "/SpeechInNoiseReportDataEntryView";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: reusableAppBar(context: context),
      bottomNavigationBar: const ReusableFooter(),
      body: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    return Obx(() {
      if (controller.isInitializing.value) {
        return const Center(child: CircularProgressIndicator());
      }

      return SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: context.width * .04),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: context.height * .04),
              _buildSectionTitle("Speech in Noise Report Data Entry", 24),
              SizedBox(height: context.height * .02),
              _buildReadOnlyIndicator(),
              const SizedBox(height: 32),
              _buildSnrLossSection(context),
              const SizedBox(height: 32),
              _buildTestDateSection(context),
              const SizedBox(height: 32),
              _buildActionButtons(context),
              const SizedBox(height: 32),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildSectionTitle(String title, double fontSize) {
    return Text(
      title,
      style: TextStyle(
        color: AppColors.charcoalBlue,
        fontSize: fontSize,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildReadOnlyIndicator() {
    return Obx(() {
      if (controller.isReadOnly.value) {
        // Read-Only Mode Indicator
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            border: Border.all(color: Colors.blue.shade200),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                Icons.lock_outline,
                color: Colors.blue.shade600,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  "Read-Only Mode: This form has been completed. Use 'Edit' to make changes or 'Reset' to start over.",
                  style: TextStyle(
                    color: Colors.blue.shade700,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        );
      } else if (controller.isFormCompleted.value && !controller.isReadOnly.value) {
        // Edit Mode Indicator (for previously completed forms)
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.orange.shade50,
            border: Border.all(color: Colors.orange.shade200),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                Icons.edit_outlined,
                color: Colors.orange.shade600,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  "Edit Mode: You are now editing a previously completed form. Changes will be saved when you submit.",
                  style: TextStyle(
                    color: Colors.orange.shade700,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        );
      } else {
        return const SizedBox.shrink();
      }
    });
  }

  Widget _buildSnrLossSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle("SNR Loss Scores", 18),
        const SizedBox(height: 8),
        const Text(
          "(SNR Loss values in dB, lower values indicate better performance)",
          style: TextStyle(
            color: AppColors.charcoalBlue,
            fontSize: 14,
            fontStyle: FontStyle.italic,
          ),
        ),
        const SizedBox(height: 16),
        _buildSnrLossRow(context),
      ],
    );
  }

  Widget _buildSnrLossRow(BuildContext context) {
    return Obx(() => Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomTextFieldForApp(
              textEditingController: controller.leftEarSnrLossController,
              hintText: "Eg: 4.5",
              title: "Unaided Left Ear SNR Loss",
              maxLength: 4,
              textInputType:
                  const TextInputType.numberWithOptions(decimal: true),
              errorText: controller.leftEarSnrLossError.value,
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
            const SizedBox(width: 16),
            CustomTextFieldForApp(
              textEditingController: controller.rightEarSnrLossController,
              hintText: "Eg: 4.5",
              maxLength: 4,
              title: "Unaided Right Ear SNR Loss",
              textInputType:
                  const TextInputType.numberWithOptions(decimal: true),
              errorText: controller.rightEarSnrLossError.value,
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
            const SizedBox(width: 16),
            Expanded(child: Container()),
          ],
        ));
  }

  Widget _buildTestDateSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle("Test Information", 18),
        const SizedBox(height: 16),
        _buildTestDateRow(context),
      ],
    );
  }

  Widget _buildTestDateRow(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Obx(
          () => CustomTextFieldForApp(
            textEditingController: controller.testDateController,
            hintText: "mm/dd/yyyy",
            inputFormatters: [DateInputFormatter()],
            title: "Test Date",
            textInputType: TextInputType.datetime,
            errorText: controller.testDateError.value,
            readOnly: controller.isReadOnly.value,
            onChanged: (value) {
              controller.validateForm();
            },
          ),
        ),
        const SizedBox(width: 16),
        Expanded(child: Container()),
        Expanded(child: Container()),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Obx(() {
      if (controller.isReadOnly.value) {
        // Show Reset and Edit buttons when in read-only mode
        return Row(
          children: [
            ReusableButton(
              width: 150,
              title: 'Reset',
              color: Colors.red,
              borderColor: Colors.red,
              onTap: () {
                controller.resetForm();
              },
            ),
            const SizedBox(width: 16),
            ReusableButton(
              width: 150,
              title: 'Edit',
              onTap: () {
                controller.enableEditMode();
              },
            ),
          ],
        );
      } else {
        // Show Submit button when in edit mode
        return ReusableButton(
          width: 150,
          title: 'Submit',
          isLoading: controller.isLoading.value,
          onTap: controller.onSaveAndContinue,
        );
      }
    });
  }
}
