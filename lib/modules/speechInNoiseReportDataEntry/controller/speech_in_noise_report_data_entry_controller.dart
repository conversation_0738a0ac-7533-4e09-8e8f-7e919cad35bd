import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gt_plus/models/speech_in_noise_details_model.dart';
import 'package:intl/intl.dart';

import '../../../services/api_service.dart';
import '../../../utils/reusableWidgets/resusable_snackbar.dart';
import '../../../utils/validators/date_validator.dart';
import '../../meta/view/meta_view.dart';
import '../../meta/controller/meta_controller.dart';
import '../view/speech_in_noise_report_data_entry_view.dart';

class SpeechInNoiseReportDataEntryController extends GetxController {
  // Score Controllers
  final leftEarSnrLossController = TextEditingController();
  final rightEarSnrLossController = TextEditingController();
  final testDateController = TextEditingController();

  // Error messages for validation
  final leftEarSnrLossError = ''.obs;
  final rightEarSnrLossError = ''.obs;
  final testDateError = ''.obs;

  RxBool isLoading = false.obs;
  final ApiService _apiService = ApiService();

  // Read-only mode management
  final RxBool isReadOnly = false.obs;
  final RxBool isFormCompleted = false.obs;
  final RxBool isInitializing = true.obs;

  @override
  void onInit() {
    super.onInit();
    // Initialize form with completion check first
    _initializeForm();
  }

  Future<void> _initializeForm() async {
    try {
      // Check completion status immediately
      await _checkCompletionStatus();

      // Then fetch saved data
      await fetchSavedSpeechInNoiseData();
    } finally {
      // Mark initialization as complete
      isInitializing.value = false;
    }
  }

  // Method to enable read-only mode
  void enableReadOnlyMode() {
    isReadOnly.value = true;
    isFormCompleted.value = true;
  }

  // Method to enable edit mode
  void enableEditMode() {
    isReadOnly.value = false;
  }

  // Method to reset form
  void resetForm() {
    // Clear all controllers
    leftEarSnrLossController.clear();
    rightEarSnrLossController.clear();
    testDateController.clear();

    // Reset observables
    isReadOnly.value = false;
    isFormCompleted.value = false;

    // Clear all error messages
    leftEarSnrLossError.value = '';
    rightEarSnrLossError.value = '';
    testDateError.value = '';
  }

  @override
  void onClose() {
    // Dispose all controllers
    leftEarSnrLossController.dispose();
    rightEarSnrLossController.dispose();
    testDateController.dispose();

    super.onClose();
  }

  // Validate SNR loss values (expected range around 0-25 dB)
  bool _validateSnrLoss(String value, RxString errorField) {
    if (value.isEmpty) {
      errorField.value = 'This field is required';
      return false;
    }

    try {
      double snrLoss = double.parse(value);
      if (snrLoss < 0 || snrLoss > 25) {
        errorField.value = 'Value must be between 0-25 dB';
        return false;
      }
      errorField.value = '';
      return true;
    } catch (e) {
      errorField.value = 'Please enter a valid number';
      return false;
    }
  }

  bool _validateTestDate(String dobText) {
    return DateValidator.validateTestDate(dobText, testDateError, 
      customErrorMessages: {
        'format': 'Enter a valid Date'
      });
  }

  // Validate all fields
  bool validateForm() {
    bool isValid = true;
    // Validate all fields
    isValid =
        _validateSnrLoss(leftEarSnrLossController.text, leftEarSnrLossError) &&
            isValid;
    isValid = _validateSnrLoss(
            rightEarSnrLossController.text, rightEarSnrLossError) &&
        isValid;
    isValid = _validateTestDate(testDateController.text) && isValid;

    return isValid;
  }

  Map<String, dynamic> getSpeechInNoiseData() {
    return {
      'LeftEarSnrLoss': leftEarSnrLossController.text,
      'RightEarSnrLoss': rightEarSnrLossController.text,
      'TestDate': testDateController.text,
    };
  }


  Future<void> postReport() async {
    try {
      isLoading.value = true;
      bool response = await _apiService.postDetails(
        type: BasicDetailTypes.SpeechInNoise,
        detailsData: getSpeechInNoiseData(),
      );
      if (response) {
        Get.offAllNamed(MetaView.routeName);
      } else {
        reusableSnackBar(message: "Something went wrong...");
      }
    } catch (e) {
      debugPrint("Error: $e");
      reusableSnackBar(message: "Something went wrong...");
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> onSaveAndContinue() async {
    if (validateForm()) {
      await postReport();
    } else {
      reusableSnackBar(message: "Please fill all required fields correctly");
    }
  }

  // Fetch saved SpeechInNoise data from API
  Future<void> fetchSavedSpeechInNoiseData() async {
    try {
      isLoading.value = true;
      final Map<String, dynamic>? response = await _apiService.getDetails(
        type: BasicDetailTypes.SpeechInNoise,
      );

      if (response != null) {
        final speechInNoiseDetails = SpeechInNoiseDetailsModel.fromJson(response);
        leftEarSnrLossController.text = speechInNoiseDetails.leftEarSnrLoss;
        rightEarSnrLossController.text = speechInNoiseDetails.rightEarSnrLoss;
        testDateController.text = speechInNoiseDetails.testDate;
      }
    } catch (e) {
      debugPrint("Error fetching saved Speech in Noise data: $e");
    } finally {
      isLoading.value = false;
    }
  }

  // Check completion status from MetaController
  Future<void> _checkCompletionStatus() async {
    try {
      final metaController = Get.find<MetaController>();
      const routeName = SpeechInNoiseReportDataEntryView.routeName;
      final isCompleted = metaController.moduleCompletionStatus[routeName] ?? false;
      final hasCompletedGTPlusOnce = metaController.hasPatientCompletedGTPlusOnce();

      debugPrint("Speech in Noise route name: $routeName");
      debugPrint("Speech in Noise completion status: $isCompleted");
      debugPrint("Has patient completed GT+ once: $hasCompletedGTPlusOnce");
      debugPrint("All completion statuses: ${metaController.moduleCompletionStatus}");

      if (isCompleted || hasCompletedGTPlusOnce) {
        debugPrint("Setting Speech in Noise form to read-only mode");
        enableReadOnlyMode();
      }
    } catch (e) {
      // MetaController might not be initialized yet, that's okay
      debugPrint("MetaController not found or not initialized: $e");
    }
  }
}
