import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../utils/appConst/app_colors.dart';
import '../../../utils/reusableWidgets/reusable_button.dart';
import '../controller/microscope_controller.dart';
import 'after_microscope_screen.dart';

class MicroscopePreviewView extends GetView<MicroscopeController> {
  static const String routeName = '/MicroscopePreviewView';

  const MicroscopePreviewView({super.key});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Preview'),
          automaticallyImplyLeading: false,
        ),
        body: Column(
          children: [
            Expanded(
              child: Obx(
                () => controller.savedImageData.value != null
                    ? Image.memory(
                        controller.savedImageData.value!,
                        fit: BoxFit.contain,
                      )
                    : const Center(child: Text('No image available')),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ReusableButton(
                    width: 200,
                    title: "Retake",
                    fontColor: AppColors.orange,
                    color: Colors.white,
                    onTap: () {
                      controller.resetImage();
                      Get.back();
                    },
                  ),
                  const SizedBox(width: 16),
                  ReusableButton(
                    width: 200,
                    title: "Next",
                    onTap: () {
                      if (controller.hasSavedImageData()) {
                        Get.toNamed(AfterMicroscopeScreen.routeName);
                      }
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
