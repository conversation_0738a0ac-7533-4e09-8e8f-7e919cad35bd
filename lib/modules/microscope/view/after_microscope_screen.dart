import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:gt_plus/modules/meta/view/meta_view.dart';
import 'package:gt_plus/modules/microscope/controller/microscope_controller.dart';
import 'package:gt_plus/utils/reusableWidgets/resusable_snackbar.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_button.dart';
import 'package:open_settings_plus/core/open_settings_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../utils/appConst/app_colors.dart';

class AfterMicroscopeScreen extends StatefulWidget {
  static const String routeName = '/AfterMicroscopeScreen';

  const AfterMicroscopeScreen({super.key});

  @override
  State<AfterMicroscopeScreen> createState() => _AfterMicroscopeScreenState();
}

class _AfterMicroscopeScreenState extends State<AfterMicroscopeScreen> {
  MicroscopeController controller = Get.find<MicroscopeController>();
  
  @override
  void initState() {
    super.initState();
    // Verify image data on screen initialization
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!controller.hasSavedImageData()) {
        // If no saved image, go back to the previous screen
        Get.back();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        appBar: AppBar(
          automaticallyImplyLeading: false,
          title: const Text('Skin'),
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const Text(
                  'Connect back to Your Wi-Fi',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),
                const Text(
                  'Please follow the steps below:',
                  style: TextStyle(fontSize: 18),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),
                _buildInstructionStep(
                  'Click on the Open Wi-Fi settings button and connect back to your clinic Wi-Fi.',
                  Icons.wifi,
                ),

                const SizedBox(height: 40),
                Row(
                  children: [
                    Expanded(
                      child: ReusableButton(
                        title: "Open Wi-Fi Settings",
                        onTap: _openWifiSettings,
                        color: AppColors.orange,
                        borderColor: AppColors.orange,
                        suffixIcon: const Icon(
                          Icons.wifi,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(
                      width: 24,
                    ),
                    Expanded(
                      child: Obx(
                        () => ReusableButton(
                          title: "Proceed",
                          isLoading: controller.isLoading.value,
                          onTap: _validateAndNavigate,
                          color: Colors.green,
                          borderColor: Colors.green,
                          suffixIcon: const Icon(
                            Icons.arrow_forward_ios_outlined,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _validateAndNavigate() async {
    // First verify we have a saved image to upload
    if (!controller.hasSavedImageData()) {
      return;
    }
    
    controller.isLoading.value = true;
    try {
      if (await _requestLocationPermission() == false) {
        return;
      }
      // Check internet connectivity
      final connectivityResult = await (Connectivity().checkConnectivity());
      if (connectivityResult.contains(ConnectivityResult.wifi)) {
        try {
          final result = await InternetAddress.lookup('google.com');
          if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
            bool isSuccess = await controller.uploadImage();
            if (isSuccess) {
              Get.offAllNamed(MetaView.routeName);
            } else {
              reusableSnackBar(message: "Something went wrong.");
            }
          }
        } on SocketException catch (_) {
          reusableSnackBar(
              message: "Please connect to Wifi with internet access...");
        }
      } else {
        reusableSnackBar(
            message: "Please connect to Wifi with internet access...");
      }
    } finally {
      controller.isLoading.value = false;
    }
  }

  Future<bool> _requestLocationPermission() async {
    if (!Platform.isAndroid && !Platform.isIOS) return true;
    final status = await Permission.location.request();
    if (!status.isGranted) {
      reusableSnackBar(message: "Location access is required to verify Wifi");
      return false;
    }
    return true;
  }

  Future<void> _openWifiSettings() async {
    try {
      switch (OpenSettingsPlus.shared) {
        case OpenSettingsPlusAndroid settings:
          settings.wifi();
          break;
        case OpenSettingsPlusIOS settings:
          settings.wifi();
          break;
        default:
          throw Exception('Platform not supported');
      }
    } catch (e) {
      reusableSnackBar(message: 'Could not open settings');
    }
  }

  Widget _buildInstructionStep(String text, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Icon(icon, size: 30, color: Colors.blue),
          const SizedBox(width: 10),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }
}
