import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../controller/microscope_controller.dart';

class MicroscopeView extends GetView<MicroscopeController> {
  static const String routeName = '/MicroscopeView';

  const MicroscopeView({super.key});

  @override
  Widget build(BuildContext context) {
    // Reset the controller when this view appears
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.resetForMicroscopeView();
    });
    
    return PopScope(
      canPop: false,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Micro Scope View'),
          automaticallyImplyLeading: false,
          actions: [
            Obx(() => IconButton(
              icon: controller.isRefreshing 
                ? const SizedBox(
                    width: 20, 
                    height: 20, 
                    child: CircularProgressIndicator(strokeWidth: 2)
                  )
                : const Icon(Icons.refresh),
              onPressed: controller.isRefreshing 
                ? null 
                : () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Resetting connection...'),
                        duration: Duration(seconds: 1),
                      ),
                    );
                    controller.refreshConnection();
                  },
            )),
          ],
        ),
        body: Center(
          child: Obx(() {
            if (!controller.isConnected.value) {
              return const Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Connecting to microscope...'),
                  SizedBox(height: 8),
                  Text('Please ensure you are connected to the microscope WiFi',
                      textAlign: TextAlign.center),
                ],
              );
            }

            if (controller.hasError.value) {
              return Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error, color: Colors.red, size: 48),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading camera view',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 24),
                    child: Text(
                      controller.errorMessage.value,
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: controller.refreshConnection,
                    child: const Text('Try Again'),
                  ),
                ],
              );
            }

            return controller.imageData.value != null
                ? Image.memory(
                    controller.imageData.value!,
                    fit: BoxFit.fill,
                    height: context.height,
                    width: context.width,
                    gaplessPlayback: true,
                    filterQuality: FilterQuality.low,
                    errorBuilder: (context, error, stackTrace) {
                      // Handle image rendering errors
                      controller.logImageError(error, stackTrace);
                      return Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(Icons.broken_image,
                              color: Colors.orange, size: 48),
                          const SizedBox(height: 16),
                          Text(
                            'Failed to render image',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const SizedBox(height: 8),
                          ElevatedButton(
                            onPressed: controller.refreshConnection,
                            child: const Text('Retry'),
                          ),
                        ],
                      );
                    },
                  )
                : Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const CircularProgressIndicator(),
                      const SizedBox(height: 16),
                      Text(
                        'Loading video stream...',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 24),
                      Text(
                        'If loading takes too long, try refreshing',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                      const SizedBox(height: 8),
                      TextButton(
                        onPressed: controller.refreshConnection,
                        child: const Text('Refresh Connection'),
                      ),
                    ],
                  );
          }),
        ),
        floatingActionButton:
            Obx(() => controller.imageData.value != null && !controller.hasError.value
                ? FloatingActionButton(
                    onPressed: controller.captureImage,
                    child: const Icon(Icons.camera),
                  )
                : const SizedBox.shrink()),
      ),
    );
  }
}
