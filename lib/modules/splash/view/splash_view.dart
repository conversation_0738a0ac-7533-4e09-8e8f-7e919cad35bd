import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gt_plus/utils/appConst/app_images.dart';

import '../controller/splash_controller.dart';

class SplashView extends GetView<SplashController> {
  const SplashView({super.key});

  static const routeName = "/SplashView";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Obx(() {
          return Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Logo section - always visible
              Image.asset(
                AppImages.logo,
                width: 200,
                height: 200,
                fit: BoxFit.contain,
              ),
              // Only show no internet message if there's no connection
              if (!controller.hasInternetConnection.value) ...[
                const SizedBox(height: 40),
                const Icon(
                  Icons.signal_wifi_off,
                  size: 40,
                  color: Colors.red,
                ),
                const SizedBox(height: 10),
                const Text(
                  "No Internet Connection",
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 5),
                const Text(
                  "Please check your connection and try again",
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 14),
                ),
                const SizedBox(height: 15),
                ElevatedButton.icon(
                  onPressed: controller.retryConnection,
                  icon: const Icon(Icons.refresh),
                  label: const Text("Retry"),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 10,
                    ),
                  ),
                ),
              ],
            ],
          );
        }),
      ),
    );
  }
}
