import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gt_plus/global_controller.dart';
import 'package:gt_plus/modules/BleFlutter/ble_flutter_controller.dart';
import 'package:gt_plus/modules/Qr/view/qr_view.dart';
import 'package:gt_plus/modules/appUpdate/package_info.dart';
import 'package:gt_plus/modules/meta/view/meta_view.dart';
import 'package:gt_plus/services/prefs_service.dart';
import 'package:gt_plus/services/remoteConfig/firebase_remote_config_service.dart';
import 'package:gt_plus/utils/appConst/app_colors.dart';
import 'package:gt_plus/utils/appConst/app_images.dart';
import 'package:gt_plus/utils/appConst/app_strings.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:open_store/open_store.dart';

import '../../login/view/login_phone_view.dart';

class SplashController extends GetxController {
  final PrefsService _prefsService = PrefsService();
  final BleFlutterController _bleFlutterController =
      Get.find<BleFlutterController>();
  final GlobalController _globalController = Get.find<GlobalController>();
  final PackageInfoSetup _packageInfo = PackageInfoSetup();
  final FirebaseRemoteConfigService _firebaseRemoteConfigService =
      FirebaseRemoteConfigService();

  final RxBool isLoading = true.obs;
  final RxBool hasInternetConnection = true.obs;

  @override
  void onInit() {
    super.onInit();
    checkInternetConnection();
  }

  Future<void> checkInternetConnection() async {
    isLoading.value = true;
    try {
      final connectivityResult = await Connectivity().checkConnectivity();

      if (connectivityResult.contains(ConnectivityResult.none)) {
        hasInternetConnection.value = false;
      } else {
        try {
          final result = await InternetAddress.lookup('google.com');
          hasInternetConnection.value =
              result.isNotEmpty && result[0].rawAddress.isNotEmpty;
        } on SocketException catch (_) {
          hasInternetConnection.value = false;
        }
      }
    } catch (e) {
      hasInternetConnection.value = false;
    } finally {
      isLoading.value = false;
      if (hasInternetConnection.value) {
        await checkForAppUpdate();
      }
    }
  }

  Future<void> retryConnection() async {
    await checkInternetConnection();
  }

  Future<void> checkForAppUpdate() async {
    try {
      final appVersion = _getExtendedVersionNumber(_packageInfo.version);

      String firebaseVersion =
          _firebaseRemoteConfigService.getMinRequiredVersion();
      final requiredMinVersion = _getExtendedVersionNumber(firebaseVersion);

      debugPrint("Current app version: $appVersion");
      debugPrint("Required min version: $requiredMinVersion");

      if (appVersion < requiredMinVersion) {
        _showUpdateDialog();
      } else {
        await checkNavigation();
      }
    } catch (e) {
      debugPrint("Error checking for app update: $e");
      await checkNavigation();
    }
  }

  void _showUpdateDialog() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      showUpdateVersionDialog(Get.context!);
    });
  }

  Future<void> showUpdateVersionDialog(BuildContext context) async {
    final materialLoginThemeColor =
        MaterialStateColor.resolveWith((states) => AppColors.orange);

    return showDialog<void>(
      context: context,
      barrierDismissible: false,

      builder: (BuildContext context) {
        return PopScope(
          canPop: false,
          child: AlertDialog(
            backgroundColor: Colors.white,
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                  height: 80,
                  width: 80,
                  child: Image.asset(AppImages.logo),
                ),
              ],
            ),
            content: SingleChildScrollView(
              child: ListBody(
                children: <Widget>[
                   const Text(
                    "New Update Available!",
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(
                    height: 8,
                  ),
                  ...getBulletPointSteps(),
                ],
              ),
            ),
            actions: <Widget>[
              Center(
                child: SizedBox(
                  width: 100,
                  child: TextButton(
                    style: ButtonStyle(
                        backgroundColor: materialLoginThemeColor,
                        shape:
                            MaterialStateProperty.all<RoundedRectangleBorder>(
                                RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(40),
                        ))),
                    child: const Text(
                      "Update",
                      style: TextStyle(
                        color: Colors.white,
                      ),
                    ),
                    onPressed: () {
                      _launchAppOrPlayStore();
                    },
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _launchAppOrPlayStore() {
    OpenStore.instance.open(
      appStoreId: AppStrings.iosAppId,
      androidAppBundleId: _packageInfo.packageName,
    );
  }

  List<Widget> getBulletPointSteps() {
    List<String> texts = [
      "We've got exciting enhancements and bug fixes in this latest version.",
      "Update now to enjoy a smoother and better experience.",
      "Tap 'Update' to get started.",
    ];
    List<Widget> steps = [];
    for (String text in texts) {
      steps.add(Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            "• ",
          ),
          SizedBox(
            width: 244,
            child: Text(
              text,
              overflow: TextOverflow.fade,
            ),
          ),
        ],
      ));
      steps.add(const SizedBox(
        height: 4,
      ));
    }

    return steps;
  }

  int _getExtendedVersionNumber(String version) {
    List versionCells = version.split('.');
    versionCells = versionCells.map((i) => int.parse(i)).toList();
    return versionCells[0] * 100000 + versionCells[1] * 1000 + versionCells[2];
  }

  Future<void> checkNavigation() async {
    await _bleFlutterController.initializeTimerValue();
    await _globalController.setGTInstructions();
    if (await _prefsService.getClinicDetails() == null) {
      if (_globalController.isTestMode.value) {
        Get.offAllNamed(LoginPhoneView.routeName);
      } else {
        Get.offAllNamed(QrView.routeName);
      }
    } else if (await _prefsService.getAccessToken() == "") {
      Get.offAllNamed(LoginPhoneView.routeName);
    } else {
      Get.offAllNamed(MetaView.routeName);
    }
  }
}
