import 'package:get/get.dart';
import '../controller/rhhi_questionnaire_controller.dart';
import '../controller/aphab_questionnaire_controller.dart';
import '../controller/tfi_questionnaire_controller.dart';
import '../controller/cosi_questionnaire_controller.dart';

class RHHIQuestionnaireBinding {
  static List<Bindings> binding = [
    BindingsBuilder(() {
      Get.lazyPut<RHHIQuestionnaireController>(() => RHHIQuestionnaireController());
    }),
  ];
}

class APHABQuestionnaireBinding {
  static List<Bindings> binding = [
    BindingsBuilder(() {
      Get.lazyPut<APHABQuestionnaireController>(() => APHABQuestionnaireController());
    }),
  ];
}

class TFIQuestionnaireBinding {
  static List<Bindings> binding = [
    BindingsBuilder(() {
      Get.lazyPut<TFIQuestionnaireController>(() => TFIQuestionnaireController());
    }),
  ];
}

class COSIQuestionnaireBinding {
  static List<Bindings> binding = [
    BindingsBuilder(() {
      Get.lazyPut<COSIQuestionnaireController>(() => COSIQuestionnaireController());
    }),
  ];
}
