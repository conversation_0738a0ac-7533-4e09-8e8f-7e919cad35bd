import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gt_plus/modules/questionnaire/controller/cosi_questionnaire_controller.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_app_bar.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_footer.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_button.dart';
import 'package:gt_plus/utils/appConst/app_colors.dart';
import 'package:gt_plus/models/questionnaire_models.dart';

class COSIQuestionnaireView extends GetView<COSIQuestionnaireController> {
  const COSIQuestionnaireView({super.key});

  static const String routeName = "/COSIQuestionnaireView";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: reusableAppBar(context: context),
      bottomNavigationBar: const ReusableFooter(),
      body: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    return Obx(() {
      if (controller.isInitializing.value) {
        return const Center(child: CircularProgressIndicator());
      }

      return _buildContent(context);
    });
  }

  Widget _buildContent(BuildContext context) {
    final questionnaireSet = controller.getQuestionnaireSet();
    if (questionnaireSet == null) {
      return const Center(
        child: Text('Failed to load questionnaire data'),
      );
    }

    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: context.width * .04),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 24),
            _buildHeader(context, questionnaireSet),
            const SizedBox(height: 20),
            _buildReadOnlyIndicator(),
            _buildDescription(context, questionnaireSet),
            const SizedBox(height: 24),
            _buildCategories(context, questionnaireSet),
            const SizedBox(height: 32),
            _buildActionButtons(context),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, QuestionnaireSet questionnaireSet) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          questionnaireSet.label,
          style: const TextStyle(
            color: AppColors.charcoalBlue,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildReadOnlyIndicator() {
    return Obx(() {
      if (controller.isReadOnly.value) {
        // Read-Only Mode Indicator
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            border: Border.all(color: Colors.blue.shade200),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                Icons.lock_outline,
                color: Colors.blue.shade600,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  "Read-Only Mode: This questionnaire has been completed. Use 'Edit' to make changes or 'Reset' to start over.",
                  style: TextStyle(
                    color: Colors.blue.shade700,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        );
      } else if (controller.isFormCompleted.value &&
          !controller.isReadOnly.value) {
        // Edit Mode Indicator (for previously completed forms)
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.orange.shade50,
            border: Border.all(color: Colors.orange.shade200),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                Icons.edit_outlined,
                color: Colors.orange.shade600,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  "Edit Mode: You are now editing a previously completed questionnaire. Changes will be saved when you submit.",
                  style: TextStyle(
                    color: Colors.orange.shade700,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        );
      } else {
        return const SizedBox.shrink();
      }
    });
  }

  Widget _buildDescription(
      BuildContext context, QuestionnaireSet questionnaireSet) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.charcoalBlue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        questionnaireSet.description,
        style: const TextStyle(
          color: AppColors.charcoalBlue,
          fontSize: 14,
          height: 1.5,
        ),
      ),
    );
  }

  Widget _buildCategories(
      BuildContext context, QuestionnaireSet questionnaireSet) {
    if (questionnaireSet.categories == null ||
        questionnaireSet.categories!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Select up to 4 listening situations and provide descriptions (* indicates mandatory):',
          style: TextStyle(
            color: AppColors.charcoalBlue,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ...List.generate(
            4, (index) => _buildDropdownItem(context, questionnaireSet, index)),
      ],
    );
  }

  Widget _buildDropdownItem(
      BuildContext context, QuestionnaireSet questionnaireSet, int index) {
    return Obx(() {
      final allCategories = controller.getAllCategories();
      final selectedCategory = controller.selectedCategories[index];
      final description = controller.descriptions[index];

      return Container(
        margin: const EdgeInsets.only(bottom: 24),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border:
              Border.all(color: AppColors.charcoalBlue.withValues(alpha: 0.3)),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Listening Situation ${index + 1}',
                  style: const TextStyle(
                    color: AppColors.charcoalBlue,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (index == 0) // Add asterisk for first dropdown (mandatory)
                  const Text(
                    ' *',
                    style: TextStyle(
                      color: Colors.red,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 12),
            DropdownButtonFormField<String>(
              value: selectedCategory,
              decoration: InputDecoration(
                hintText: 'Select a listening situation',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                filled: controller.isReadOnly.value,
                fillColor: controller.isReadOnly.value ? Colors.grey.shade200 : null,
              ),
              items: [
                const DropdownMenuItem<String>(
                  value: null,
                  child: Text('Select a situation'),
                ),
                ...allCategories.map((categoryValue) {
                  final category = questionnaireSet.categories!
                      .firstWhere((c) => c.value == categoryValue);
                  final isDisabled = controller.isCategoryDisabled(categoryValue, index);

                  return DropdownMenuItem<String>(
                    value: categoryValue,
                    enabled: !isDisabled,
                    child: Text(
                      category.label,
                      style: TextStyle(
                        fontSize: 14,
                        color: isDisabled ? Colors.grey : null,
                      ),
                    ),
                  );
                }),
              ],
              onChanged: controller.isReadOnly.value ? null : (value) {
                controller.updateDropdownSelection(index, value);
              },
            ),
            if (selectedCategory != null) ...[
              const SizedBox(height: 16),
              Text(
                'Description for ${controller.getCategoryLabel(selectedCategory)}:',
                style: const TextStyle(
                  color: AppColors.charcoalBlue,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              TextFormField(
                initialValue: description,
                readOnly: controller.isReadOnly.value,
                decoration: InputDecoration(
                  hintText:
                      'Please describe your experience with this listening situation',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: const EdgeInsets.all(12),
                  filled: controller.isReadOnly.value,
                  fillColor: controller.isReadOnly.value ? Colors.grey.shade200 : null,
                ),
                maxLines: 3,
                onChanged: controller.isReadOnly.value ? null : (value) {
                  controller.updateDescription(index, value);
                },
              ),
            ],
          ],
        ),
      );
    });
  }

  Widget _buildActionButtons(BuildContext context) {
    return Obx(() {
      if (controller.isReadOnly.value) {
        // Show Reset and Edit buttons when in read-only mode
        return Row(
          children: [
            ReusableButton(
              width: context.width * .22,
              title: 'Reset',
              color: Colors.red,
              borderColor: Colors.red,
              onTap: () {
                controller.resetForm();
              },
            ),
            const SizedBox(width: 16),
            ReusableButton(
              width: context.width * .22,
              title: 'Edit',
              onTap: () {
                controller.enableEditMode();
              },
            ),
          ],
        );
      } else {
        // Show Submit button when in edit mode
        return ReusableButton(
          width: context.width * .22,
          title: 'Submit',
          onTap: controller.submitQuestionnaire,
          isLoading: controller.isSubmitting.value,
        );
      }
    });
  }
}
