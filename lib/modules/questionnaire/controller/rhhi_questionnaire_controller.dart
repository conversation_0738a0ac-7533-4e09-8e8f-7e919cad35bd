import 'package:gt_plus/enums/questionnaire_type_enum.dart';
import 'base_questionnaire_controller.dart';
import '../view/rhhi_questionnaire_view.dart';

class RHHIQuestionnaireController extends BaseQuestionnaireController {
  @override
  QuestionnaireType get questionnaireType => QuestionnaireType.rhhiQuest;

  @override
  String get questionnaireKey => 'setOne'; // RHHI is in setOne based on the demo data

  @override
  String get routeName => RHHIQuestionnaireView.routeName;
}
