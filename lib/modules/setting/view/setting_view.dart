import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:gt_plus/modules/setting/controller/setting_controller.dart';
import 'package:gt_plus/utils/appConst/app_images.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_app_bar.dart';

import '../../../utils/appConst/app_colors.dart';
import '../../../utils/reusableWidgets/custom_text_field_for_app.dart';
import '../../../utils/reusableWidgets/reusable_button.dart';

class SettingView extends GetView<SettingController> {
  const SettingView({super.key});

  static const routeName = "/SettingView";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: reusableAppBar(
        context: context,
        actions: [],
      ),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildClinicInfoCard(),
                      const SizedBox(height: 20),
                      _buildSupportCard(),  
                      const SizedBox(height: 20),
                      _buildTestModeCard(),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildClinicInfoCard() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: Colors.deepOrange,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding:
            const EdgeInsets.only(left: 24.0, top: 24, bottom: 24, right: 28),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Clinic Information',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.deepOrange,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'Clinic: ',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                  ),
                ),
                Obx(() => Text(
                      controller.clinicName.value,
                      style: const TextStyle(
                        fontWeight: FontWeight.w500,
                        color: AppColors.gray2,
                      ),
                    )),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'Program: ',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Obx(() => Text(
                      controller.programName.value,
                      style: const TextStyle(
                        fontWeight: FontWeight.w500,
                        color: AppColors.gray2,
                      ),
                    )),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSupportCard() {
    return Card(
      color: Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            GestureDetector(
              onTap: () => controller.isTechnicalSupportExpanded.toggle(),
              child: Row(
                children: [
                  SvgPicture.asset(AppImages.icSupport, width: 28, height: 28),
                  const SizedBox(width: 8),
                  const Text(
                    'Support',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  Obx(() => Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.orange.shade100,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          controller.isTechnicalSupportExpanded.value
                              ? Icons.keyboard_arrow_up
                              : Icons.keyboard_arrow_down,
                          color: Colors.deepOrange,
                        ),
                      )),
                ],
              ),
            ),
            Obx(() => controller.isTechnicalSupportExpanded.value
                ? Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          CustomTextFieldForApp(
                            textEditingController:
                                controller.issueTitleController,
                            hintText: 'Describe your issue in one line',
                            title: "Describe the issue you're experiencing",
                            onChanged: (value) {},
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          CustomTextFieldForApp(
                            textEditingController:
                                controller.issueDescriptionController,
                            hintText: 'Please describe the issue in detail',
                            title: "Description",
                            maxLines: 4,
                            onChanged: (value) {},
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Center(
                        child: Obx(() => ReusableButton(
                              title: controller.isSubmitting.value
                                  ? 'Submitting...'
                                  : 'Submit Request',
                              isLoading: controller.isSubmitting.value,
                              onTap: () => controller.submitTechnicalSupport(),
                              color: AppColors.orange,
                              width: 200,
                              height: 48,
                              fontSize: 16,
                              radius: 8,
                            )),
                      ),
                    ],
                  )
                : const SizedBox.shrink()),
          ],
        ),
      ),
    );
  }

  Widget _buildTestModeCard() {
    return Card(
      color: Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            GestureDetector(
              onTap: () => controller.isTestModeExpanded.toggle(),
              child: Row(
                children: [
                  SvgPicture.asset(AppImages.icTestMode, width: 28, height: 28),
                  const SizedBox(width: 12),
                  const Text(
                    'Test Mode Setting',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  Obx(() => Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.orange.shade100,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          controller.isTestModeExpanded.value
                              ? Icons.keyboard_arrow_up
                              : Icons.keyboard_arrow_down,
                          color: Colors.deepOrange,
                        ),
                      )),
                ],
              ),
            ),
            Obx(
              () => controller.isTestModeExpanded.value
                  ? Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 12),
                        Obx(() => SwitchListTile(
                              title: Row(
                                children: [
                                  const Text('Enable Test Mode'),
                                  if (controller.isTestModeLoading.value)
                                    const Padding(
                                      padding: EdgeInsets.only(left: 8.0),
                                      child: SizedBox(
                                        height: 16,
                                        width: 16,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          color: Colors.deepOrange,
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                              subtitle: controller.isTestModeLoading.value
                                  ? Text(
                                      controller.isTestMode.value
                                          ? 'Turning on test mode...'
                                          : 'Turning off test mode...',
                                      style: const TextStyle(
                                        fontStyle: FontStyle.italic,
                                      ),
                                    )
                                  : const Text(
                                      'This mode allows you to practice or showcase a demo without creating an insights report.'),
                              value: controller.isTestMode.value,
                              onChanged: controller.isTestModeLoading.value
                                  ? null
                                  : (value) => controller.toggleTestMode(value),
                              activeColor: Colors.deepOrange,
                              contentPadding: EdgeInsets.zero,
                            )),
                        // const Divider(),
                        // SwitchListTile(
                        //   title: const Text('Reset Application'),
                        //   subtitle: const Text(
                        //       'Clear all local data and return to registration.'),
                        //   value: false,
                        //   onChanged: (_) => controller.showResetConfirmation(),
                        //   activeColor: Colors.deepOrange,
                        //   contentPadding: EdgeInsets.zero,
                        // ),
                      ],
                    )
                  : const SizedBox.shrink(),
            ),
          ],
        ),
      ),
    );
  }

  // Widget _buildResetAppButton() {
  //   return Center(
  //     child: ElevatedButton(
  //       onPressed: () => controller.showResetConfirmation(),
  //       style: ElevatedButton.styleFrom(
  //         backgroundColor: Colors.deepOrange,
  //         padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
  //         shape: RoundedRectangleBorder(
  //           borderRadius: BorderRadius.circular(8),
  //         ),
  //       ),
  //       child: const Text(
  //         'Reset App',
  //         style: TextStyle(
  //           color: Colors.white,
  //           fontSize: 16,
  //           fontWeight: FontWeight.w500,
  //         ),
  //       ),
  //     ),
  //   );
  // }
}
