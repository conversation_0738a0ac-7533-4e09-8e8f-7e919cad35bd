import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gt_plus/modules/Qr/view/qr_view.dart';
import 'package:gt_plus/services/prefs_service.dart';
import 'package:gt_plus/utils/reusableWidgets/resusable_snackbar.dart';
import 'package:gt_plus/global_controller.dart';
import 'package:gt_plus/services/api_service.dart';

import '../../../models/clinic_details_model.dart';
import '../../BleFlutter/ble_flutter_controller.dart';
import '../../login/view/login_phone_view.dart';

class SettingController extends GetxController {
  RxString clinicName = ''.obs;
  RxString programName = ''.obs;
  RxList<String> kitNames = <String>[].obs;
  RxBool paymentFlag = false.obs;
  RxBool showProvider = false.obs;

  // Observable variables for switches
  final isTestMode = false.obs;
  final isTestModeLoading = false.obs;

  // Technical Support section
  final isTechnicalSupportExpanded = false.obs;
  final issueTitleController = TextEditingController();
  final issueDescriptionController = TextEditingController();
  final isSubmitting = false.obs;

  // Test Mode section
  final isTestModeExpanded = false.obs;

  final PrefsService _prefsService = PrefsService();
  final ApiService _apiService = ApiService();
  final _bleFlutterController = Get.find<BleFlutterController>();
  final _globalController = Get.find<GlobalController>();

  @override
  void onInit() {
    super.onInit();
    loadSettings();
    getAndSetClinicInfo();
  }

  @override
  void onClose() {
    issueTitleController.dispose();
    issueDescriptionController.dispose();
    super.onClose();
  }

  Future<void> loadSettings() async {
    isTestMode.value = await _prefsService.getDemoMode();
  }

  Future<void> getAndSetClinicInfo() async {
    ClinicDetailsModel? clinicDetailsModel =
        await _prefsService.getClinicDetails();
    clinicName.value = clinicDetailsModel?.clinicName ?? "";
    programName.value = clinicDetailsModel?.programName ?? "";
    kitNames.value = clinicDetailsModel?.kitName ?? [];
    paymentFlag.value = clinicDetailsModel?.paymentFlag ?? false;
    showProvider.value = clinicDetailsModel?.showProvider ?? false;
  }

  void toggleTestMode(bool value) async {
    isTestModeLoading.value = true;
    isTestMode.value = value;
    await _prefsService.setDemoMode(value);
    _globalController.isTestMode.value = value;
    if (Get.currentRoute != LoginPhoneView.routeName &&
        Get.previousRoute != LoginPhoneView.routeName) {
      // Clear all local saved data when test mode is toggled
      await _clearLocalDataExceptDemoMode();
      if (!value) {
        await getClinicInfoIfNotExist();
      }
      isTestModeLoading.value = false;
      Get.offAllNamed(LoginPhoneView.routeName);
    } else {
      if (!value) {
        await getClinicInfoIfNotExist();
      }
      isTestModeLoading.value = false;
    }
  }

  Future<void> getClinicInfoIfNotExist() async {
    ClinicDetailsModel? clinicDetailsModelFromPrefs =
        await _prefsService.getClinicDetails();

    if (clinicDetailsModelFromPrefs == null) {
      String qrValue = await _prefsService.getQrValue();
      if (qrValue != "") {
        ClinicDetailsModel? clinicDetailsModel =
            await _apiService.getClinicDetails(qrValue);
        if (clinicDetailsModel != null) {
          _prefsService.setClinicDetails(clinicDetailsModel);
        }
      }
    }
  }

  /// Clears all local data except the demo mode setting
  Future<void> _clearLocalDataExceptDemoMode() async {
    // Save the current demo mode setting
    bool currentDemoMode = isTestMode.value;
    String qrValue = await _prefsService.getQrValue();

    // Clear all data
    await _bleFlutterController.disconnectAndResetAll();
    await _prefsService.clearAll();

    // Restore the demo mode setting
    await _prefsService.setDemoMode(currentDemoMode);
    await _prefsService.setQrValue(qrValue: qrValue);
    isTestMode.value = currentDemoMode;
    _globalController.isTestMode.value = currentDemoMode;
  }

  void showResetConfirmation() {
    Get.dialog(
      AlertDialog(
        title: const Text('Reset Application'),
        content: const Text(
            'Are you sure you want to reset the app? This will clear all data and return to default settings. This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              resetApp();
            },
            child: const Text('Reset', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void resetApp() async {
    await _bleFlutterController.disconnectAndResetAll();
    await _prefsService.clearAll();

    // Make sure test mode is turned off
    isTestMode.value = false;
    await _prefsService.setDemoMode(false);
    _globalController.isTestMode.value = false;

    reusableSnackBar(
      message: "Application has been reset to default settings",
      isForError: false,
    );

    // Wait a moment for the snackbar to be visible before navigating
    await Future.delayed(const Duration(seconds: 1));
    Get.offAllNamed(QrView.routeName);
  }

  Future<void> submitTechnicalSupport() async {
    if (issueTitleController.text.trim().isEmpty ||
        issueDescriptionController.text.trim().isEmpty) {
      reusableSnackBar(
        message: "Please fill in both the issue title and description",
        isForError: true,
      );
      return;
    }

    isSubmitting.value = true;

    try {
      final result = await _apiService.postTechnicalSupport(
        issueTitle: issueTitleController.text.trim(),
        issueDescription: issueDescriptionController.text.trim(),
        clinicName: clinicName.value,
        programName: programName.value,
        kitName: kitNames,
      );

      if (result != null) {
        reusableSnackBar(
          message: "Your support request has been submitted successfully",
          isForError: false,
        );
        // Clear the form
        issueTitleController.clear();
        issueDescriptionController.clear();
        // Close the expandable section
        isTechnicalSupportExpanded.value = false;
      } else {
        reusableSnackBar(
          message: "Failed to submit support request. Please try again.",
          isForError: true,
        );
      }
    } catch (e) {
      reusableSnackBar(
        message: "An error occurred: $e",
        isForError: true,
      );
    } finally {
      isSubmitting.value = false;
    }
  }
}
