import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:country_picker/country_picker.dart';
import 'package:gt_plus/models/clinic_details_model.dart';
import 'package:gt_plus/models/login_model.dart';
import 'package:gt_plus/modules/meta/view/meta_view.dart';
import 'package:gt_plus/services/analytics/events.dart';
import 'package:gt_plus/services/api_service.dart';
import 'package:gt_plus/services/prefs_service.dart';
import 'package:gt_plus/utils/reusableWidgets/resusable_snackbar.dart';

import '../view/login_dob_view.dart';

class LoginController extends GetxController {


  final phoneNumberController = TextEditingController();
  final emailController = TextEditingController();
  final firstNameController = TextEditingController();
  final lastNameController = TextEditingController();
  final dobController = TextEditingController();

  final RxBool isPhoneValid = false.obs;
  final RxBool isEmailValid = true.obs;
  // Validation for new fields
  final RxBool isFirstNameValid = false.obs;
  final RxBool isLastNameValid = false.obs;
  final RxBool isDateOfBirthValid = false.obs;
  RxBool isButtonLoading = false.obs;

  // Error messages for fields
  final RxString firstNameError = "".obs;
  final RxString lastNameError = "".obs;
  final RxString dateOfBirthError = "".obs;
  final RxString phoneNumberError = "".obs;
  final RxString emailError = "".obs;

  // Country picker related variables
  final RxString countryCode = "1".obs;
  final RxString countryFlag = "🇺🇸".obs;

  final PrefsService _prefsService = PrefsService();
  final ApiService _apiService = ApiService();

  // Computed property for form validation
  bool get isFormValid => isPhoneValid.value && isEmailValid.value;

  // Computed property for profile form validation
  bool get isProfileFormValid =>
      isFirstNameValid.value &&
      isLastNameValid.value &&
      isDateOfBirthValid.value;

  @override
  void onInit() {
    super.onInit();
    // Add listeners to validate on change
    phoneNumberController.addListener(() {
      validatePhoneNumber(phoneNumberController.text);
    });
    emailController.addListener(() {
      validateEmail(emailController.text);
    });
    // Add listeners for new fields
    firstNameController.addListener(() {
      validateFirstName(firstNameController.text);
    });
    lastNameController.addListener(() {
      validateLastName(lastNameController.text);
    });
    dobController.addListener(() {
      validateDateOfBirth(dobController.text);
    });
  }

  @override
  void onClose() {
    // Clean up controllers
    phoneNumberController.dispose();
    emailController.dispose();
    firstNameController.dispose();
    lastNameController.dispose();
    dobController.dispose();
    super.onClose();
  }

  void updateSelectedCountry(Country country) {
    countryCode.value = country.phoneCode;
    countryFlag.value = country.flagEmoji;
    validatePhoneNumber(phoneNumberController.text);
  }

  void validatePhoneNumber(String value) {
    final cleanedValue = value.trim();

    final bool isValid =
        cleanedValue.length > 9 && RegExp(r'^\d+$').hasMatch(cleanedValue);
    isPhoneValid.value = isValid;
    phoneNumberError.value = isValid ? "" : "Please enter a valid phone number";
  }

  void validateEmail(String value) {
    if (value.isEmpty) {
      isEmailValid.value = true;
      emailError.value = "";
      return;
    }
    const emailPattern = r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$';
    final bool isValid = RegExp(emailPattern).hasMatch(value.trim());
    isEmailValid.value = isValid;
    emailError.value = isValid ? "" : "Please enter a valid email address";
  }

  // New validation methods
  void validateFirstName(String value) {
    final result = _validateNameField(value);
    isFirstNameValid.value = result.isValid;
    firstNameError.value = result.isValid
        ? ""
        : result.errorMessage ?? "Please enter your first name";
  }

  void validateLastName(String value) {
    final result = _validateNameField(value);
    isLastNameValid.value = result.isValid;
    lastNameError.value = result.isValid
        ? ""
        : result.errorMessage ?? "Please enter your last name";
  }

  // Helper method for name validation
  ({bool isValid, String? errorMessage}) _validateNameField(String value) {
    if (value.trim().isEmpty) {
      return (isValid: false, errorMessage: "This field is required");
    }

    // Use the same regex as BasicDetailView - only letters, no spaces
    if (!RegExp(r'^[a-zA-Z]+$').hasMatch(value)) {
      return (isValid: false, errorMessage: "Should contain only letters");
    }

    return (isValid: true, errorMessage: null);
  }

  // Add date of birth validation method
  void validateDateOfBirth(String value) {
    if (value.isEmpty) {
      isDateOfBirthValid.value = false;
      dateOfBirthError.value = "Please enter your date of birth";
      return;
    }

    // Check if format is MM/DD/YYYY
    final RegExp dateRegex = RegExp(r'^\d{2}/\d{2}/\d{4}$');
    if (!dateRegex.hasMatch(value)) {
      isDateOfBirthValid.value = false;
      dateOfBirthError.value = "Invalid date format (MM/DD/YYYY)";
      return;
    }

    try {
      // Parse the date
      List<String> dateParts = value.split('/');
      int month = int.parse(dateParts[0]);
      int day = int.parse(dateParts[1]);
      int year = int.parse(dateParts[2]);

      // Basic validation
      if (month < 1 ||
          month > 12 ||
          day < 1 ||
          day > 31 ||
          year < 1900 ||
          year > DateTime.now().year) {
        isDateOfBirthValid.value = false;
        dateOfBirthError.value = "Please enter a valid date";
        return;
      }

      // Try to create a DateTime object to validate the date further
      DateTime date = DateTime(year, month, day);

      // Check if the date is not in the future
      if (date.isAfter(DateTime.now())) {
        isDateOfBirthValid.value = false;
        dateOfBirthError.value = "Date of birth cannot be in the future";
        return;
      }

      isDateOfBirthValid.value = true;
      dateOfBirthError.value = "";
    } catch (e) {
      isDateOfBirthValid.value = false;
      dateOfBirthError.value = "Please enter a valid date";
    }
  }

  Future<void> submitForm() async {
    validateEmail(emailController.text);
    validatePhoneNumber(phoneNumberController.text);

    if (isFormValid) {
      try {
        isButtonLoading.value = true;

        final fullPhoneNumber =
            '+${countryCode.value}${phoneNumberController.text}';

        debugPrint(
            'Submitting form with phone: $fullPhoneNumber, email: ${emailController.text}');

        LoginModel? loginModel;

        LogEvents.logLoginGTEvent(
          phoneNumber: fullPhoneNumber,
          email: emailController.text.trim(),
        );

        ClinicDetailsModel? clinicDetailsModel =
            await _prefsService.getClinicDetails();

        if (emailController.text != "") {
          loginModel = await _apiService.login(
              fullPhoneNumber, clinicDetailsModel?.clinicName ?? "",
              email: emailController.text.trim());
        } else {
          loginModel = await _apiService.login(
              fullPhoneNumber, clinicDetailsModel?.clinicName ?? "");
        }

        if (loginModel != null) {
          await _prefsService.setAccessToken(loginModel.accessToken ?? "");
          await _prefsService.setRefreshToken(loginModel.refreshToken ?? "");
          await _prefsService.setIdentifier(loginModel.identifier ?? "");
          
          // Store login method as phone
          await _prefsService.setLoginMethod("phone");

          Get.offAllNamed(MetaView.routeName);
        } else {
          reusableSnackBar(
              message: "Something went wrong, we are working on it");
        }
      } catch (e) {
        reusableSnackBar(message: "Something went wrong, we are working on it");
      } finally {
        isButtonLoading.value = false;
      }
    }
  }

  // Navigate to profile details screen
  void goToProfileDetails() {
    Get.toNamed(LoginDobView.routeName);
  }

  // Submit profile form
  Future<void> submitProfileForm() async {
    validateFirstName(firstNameController.text);
    validateLastName(lastNameController.text);
    validateDateOfBirth(dobController.text);

    if (isProfileFormValid) {
      try {
        isButtonLoading.value = true;

        debugPrint(
            'Submitting profile form with firstName: ${firstNameController.text}, lastName: ${lastNameController.text}, dob: ${dobController.text}');

        // Prepare the request body with profile details
        final body = {
          "firstName": firstNameController.text.trim(),
          "lastName": lastNameController.text.trim(),
          "dateOfBirth": dobController.text,
        };

        ClinicDetailsModel? clinicDetailsModel =
            await _prefsService.getClinicDetails();

        LoginModel? loginModel = await _apiService.login(
            "", clinicDetailsModel?.clinicName ?? "",
            additionalData: body);

        if (loginModel != null) {
          await _prefsService.setAccessToken(loginModel.accessToken ?? "");
          await _prefsService.setRefreshToken(loginModel.refreshToken ?? "");
          await _prefsService.setIdentifier(loginModel.identifier ?? "");

          // Store DOB and patient names in shared preferences
          await _prefsService.setDobFromLogin(dobController.text);
          String fullName = "${firstNameController.text.trim()} ${lastNameController.text.trim()}";
          await _prefsService.setPatientName(fullName);
          await _prefsService.setFirstNameFromLogin(firstNameController.text.trim());
          await _prefsService.setLastNameFromLogin(lastNameController.text.trim());
          await _prefsService.setLoginMethod("dob");

          Get.offAllNamed(MetaView.routeName);
        } else {
          reusableSnackBar(
              message: "Something went wrong, we are working on it");
        }
      } catch (e) {
        reusableSnackBar(message: "Something went wrong, we are working on it");
      } finally {
        isButtonLoading.value = false;
      }
    }
  }
}
