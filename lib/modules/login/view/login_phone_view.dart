import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gt_plus/utils/reusableWidgets/custom_text_field_for_app.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_app_bar.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_button.dart';
import 'package:country_picker/country_picker.dart';
import '../../../utils/appConst/app_colors.dart';
import '../controller/login_controller.dart';

class LoginPhoneView extends GetView<LoginController> {
  const LoginPhoneView({super.key});
  static const routeName = "/LoginPhoneView";
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: reusableAppBar(context: context),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: context.width * .05),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                height: context.height * .05,
              ),
              const Text(
                "Enter Patient Contact Information",
                style: TextStyle(
                  color: AppColors.black,
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(
                height: 16,
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    "Phone Number",
                    style: TextStyle(
                      color: AppColors.black,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Country Code Selector
                      Obx(() => GestureDetector(
                            onTap: () {
                              showCountryPicker(
                                context: context,
                                showPhoneCode: true,
                                onSelect: (Country country) {
                                  controller.updateSelectedCountry(country);
                                },
                                countryFilter: const ['IN', 'US'],
                                searchAutofocus: false,
                                showSearch: false,
                                useSafeArea: true,
                                countryListTheme: CountryListThemeData(
                                  borderRadius: BorderRadius.circular(24),
                                ),
                              );
                            },
                            child: Container(
                              height: 56,
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 12),
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    controller.countryFlag.value,
                                    style: const TextStyle(fontSize: 24),
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    "+${controller.countryCode.value}",
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  const Icon(Icons.arrow_drop_down),
                                ],
                              ),
                            ),
                          )),
                      const SizedBox(width: 8),
                      // Phone Number TextField
                      Expanded(
                        child: TextField(
                          controller: controller.phoneNumberController,
                          keyboardType: TextInputType.phone,
                          maxLength: 10,
                          decoration: const InputDecoration(
                            hintText: "Phone Number",
                            border: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: AppColors.gray,
                              ),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: AppColors.gray,
                              ),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: AppColors.gray,
                              ),
                            ),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 16, vertical: 16),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              Obx(() => controller.phoneNumberError.value.isNotEmpty
                  ? Align(
                      alignment: Alignment.centerLeft,
                      child: Padding(
                        padding: const EdgeInsets.only(top: 4.0),
                        child: Text(
                          controller.phoneNumberError.value,
                          style: const TextStyle(
                            color: Colors.red,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    )
                  : const SizedBox.shrink()),
              const SizedBox(
                height: 24,
              ),
              Row(
                children: [
                  CustomTextFieldForApp(
                    textEditingController: controller.emailController,
                    hintText: "Enter Your Email",
                    title: "Email",
                    textInputType: TextInputType.emailAddress,
                    isRequired: false,
                  ),
                ],
              ),
              Obx(() => controller.emailError.value.isNotEmpty
                  ? Align(
                      alignment: Alignment.centerLeft,
                      child: Padding(
                        padding: const EdgeInsets.only(top: 4.0),
                        child: Text(
                          controller.emailError.value,
                          style: const TextStyle(
                            color: Colors.red,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    )
                  : const SizedBox.shrink()),
              SizedBox(
                height: context.height * .07,
              ),
              Obx(
                () => ReusableButton(
                  width: 250,
                  radius: 8,
                  title: "Submit",
                  isLoading: controller.isButtonLoading.value,
                  onTap: () {
                    controller.submitForm();
                  },
                ),
              ),
              const SizedBox(
                height: 24,
              ),
              GestureDetector(
                onTap: () {
                  controller.goToProfileDetails();
                },
                child: const Text(
                  "Don't have a phone number? Sign up with personal details",
                  style: TextStyle(
                    color: AppColors.brightScarlet,
                    fontSize: 18,
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
