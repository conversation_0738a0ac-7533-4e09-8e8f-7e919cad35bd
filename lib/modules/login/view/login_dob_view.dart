import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gt_plus/utils/appConst/app_colors.dart';
import 'package:gt_plus/utils/reusableWidgets/custom_text_field_for_app.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_app_bar.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_button.dart';
import 'package:gt_plus/utils/date_input_fomatter.dart';

import '../controller/login_controller.dart';

class LoginDobView extends GetView<LoginController> {
  const LoginDobView({super.key});
  static const routeName = "/LoginDobView";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: reusableAppBar(context: context),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: context.width * .05),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                height: context.height * .05,
              ),
              const Text(
                "Enter Patient's Information",
                style: TextStyle(
                  color: AppColors.black,
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(
                height: 16,
              ),
              Row(
                children: [
                  Obx(
                    () => CustomTextFieldForApp(
                      textEditingController: controller.firstNameController,
                      hintText: "First Name",
                      title: "First Name",
                      textInputType: TextInputType.name,
                      errorText: controller.firstNameError.value,
                      onChanged: (value) {
                        controller.validateFirstName(value);
                      },
                    ),
                  )
                ],
              ),
              const SizedBox(
                height: 24,
              ),
              Row(
                children: [
                  Obx(
                    () => CustomTextFieldForApp(
                      textEditingController: controller.lastNameController,
                      hintText: "Last Name",
                      title: "Last Name",
                      textInputType: TextInputType.name,
                      errorText: controller.lastNameError.value,
                      onChanged: (value) {
                        controller.validateLastName(value);
                      },
                    ),
                  )
                ],
              ),
              const SizedBox(
                height: 24,
              ),
              Row(
                children: [
                  Obx(
                    () => CustomTextFieldForApp(
                      textEditingController: controller.dobController,
                      hintText: "MM/DD/YYYY",
                      title: "Date of Birth",
                      textInputType: TextInputType.datetime,
                      inputFormatters: [DateInputFormatter()],
                      errorText: controller.dateOfBirthError.value,
                      onChanged: (value) {
                        controller.validateDateOfBirth(value);
                      },
                    ),
                  )
                ],
              ),
              SizedBox(
                height: context.height * .07,
              ),
              Obx(
                () => ReusableButton(
                  width: 250,
                  radius: 8,
                  title: "Submit",
                  isLoading: controller.isButtonLoading.value,
                  onTap: () {
                    controller.submitProfileForm();
                  },
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
