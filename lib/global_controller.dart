import 'package:get/get.dart';
import 'package:gt_plus/models/gt_instructions_model.dart';
import 'package:gt_plus/services/remoteConfig/firebase_remote_config_service.dart';
import 'package:gt_plus/services/prefs_service.dart';

class GlobalController extends GetxController {
  final FirebaseRemoteConfigService _config = FirebaseRemoteConfigService();
  final PrefsService _prefsService = PrefsService();

  final isTestMode = false.obs;
  
  late GTInstructionsModel instructions;

  @override
  void onInit() {
    super.onInit();
    loadDemoMode();
  }

  Future<void> loadDemoMode() async {
    isTestMode.value = await _prefsService.getDemoMode();
  }

  Future<void> setGTInstructions() async {
    instructions = _config.getInstructions();
  }
}
