enum QuestionnaireType {
  aphabQuest(
    title: "APHAB Questionnaire",
    apiFieldName: "APHABQuest",
    description: "Abbreviated Profile of Hearing Aid Benefit",
  ),
  cosiQuest(
    title: "COSI Questionnaire",
    apiFieldName: "COSIQuest",
    description: "Client Oriented Scale of Improvement",
  ),
  tfiQuest(
    title: "TFI Questionnaire",
    apiFieldName: "TFIQuest",
    description: "Tinnitus Functional Index",
  ),
  rhhiQuest(
    title: "RHHI Questionnaire",
    apiFieldName: "RHHIQuest",
    description: "Revised Hearing Handicap Inventory",
  );

  const QuestionnaireType({
    required this.title,
    required this.apiFieldName,
    required this.description,
  });

  final String title;
  final String apiFieldName;
  final String description;
}
