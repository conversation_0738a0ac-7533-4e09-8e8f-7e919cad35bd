// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCHZ7FSKWQ_gx7s5SRr9TGfEdor8L81BD8',
    appId: '1:391397853270:android:6fac903f8918db453e13f7',
    messagingSenderId: '391397853270',
    projectId: 'sfoto-clinic-396605',
    databaseURL: 'https://sfoto-clinic-396605-default-rtdb.firebaseio.com',
    storageBucket: 'sfoto-clinic-396605.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBIzELriqo8F2EqFi4urNv-MjHzxp06AgQ',
    appId: '1:391397853270:ios:6939e7330446f9403e13f7',
    messagingSenderId: '391397853270',
    projectId: 'sfoto-clinic-396605',
    databaseURL: 'https://sfoto-clinic-396605-default-rtdb.firebaseio.com',
    storageBucket: 'sfoto-clinic-396605.firebasestorage.app',
    iosBundleId: 'com.saiwell.gt',
  );
}
