import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';

class BleReaderPage extends StatefulWidget {
  const BleReaderPage({super.key});
  static const String routeName = "/BleReaderPage";

  @override
  State<BleReaderPage> createState() => _BleReaderPageState();
}

class _BleReaderPageState extends State<BleReaderPage> {
  // List to store discovered devices
  final List<BluetoothDevice> devicesList = [];

  // Selected device
  BluetoothDevice? selectedDevice;

  // Selected characteristic for reading
  BluetoothCharacteristic? selectedCharacteristic;

  // Stream subscription for notifications
  StreamSubscription<List<int>>? notificationSubscription;

  // Status message
  String statusMessage = "Idle";

  // Data received from BLE
  String receivedData = "No data received yet";

  @override
  void initState() {
    super.initState();
    // Register listeners for Bluetooth state changes
    FlutterBluePlus.adapterState.listen((state) {
      if (state == BluetoothAdapterState.on) {
        setState(() {
          statusMessage = "Bluetooth is on";
        });
      } else if (state == BluetoothAdapterState.off) {
        setState(() {
          statusMessage = "Bluetooth is off";
          devicesList.clear();
        });
      }
    });
  }

  @override
  void dispose() {
    // Cancel notification subscription when disposing
    notificationSubscription?.cancel();

    // Disconnect device
    selectedDevice?.disconnect();
    super.dispose();
  }

  // Start scanning for devices
  void startScan() {
    setState(() {
      devicesList.clear();
      statusMessage = "Scanning...";
    });

    // Start scanning
    FlutterBluePlus.startScan(timeout: const Duration(seconds: 5));

    // Listen to scan results
    FlutterBluePlus.scanResults.listen((results) {
      for (ScanResult result in results) {
        if (result.device.platformName.isNotEmpty &&
            !devicesList.contains(result.device)) {
          setState(() {
            devicesList.add(result.device);
          });
        }
      }
    });

    // When scan completes
    FlutterBluePlus.isScanning.listen((isScanning) {
      if (!isScanning) {
        setState(() {
          statusMessage = "Scan complete. Found ${devicesList.length} devices.";
        });
      }
    });
  }

  // Connect to selected device
  Future<void> connectToDevice(BluetoothDevice device) async {
    setState(() {
      statusMessage = "Connecting to ${device.platformName}...";
    });

    try {
      await device.connect();
      setState(() {
        selectedDevice = device;
        statusMessage = "Connected to ${device.platformName}";
      });

      // Discover services after connecting
      discoverServices();
    } catch (e) {
      setState(() {
        statusMessage = "Failed to connect: $e";
      });
    }
  }

  // Discover services for the connected device
  Future<void> discoverServices() async {
    if (selectedDevice == null) return;

    setState(() {
      statusMessage = "Discovering services...";
    });

    try {
      List<BluetoothService> services =
          await selectedDevice!.discoverServices();
      setState(() {
        statusMessage = "Found ${services.length} services";
      });

      // Show service and characteristic selection dialog
      _showServiceSelectionDialog(services);
    } catch (e) {
      setState(() {
        statusMessage = "Failed to discover services: $e";
      });
    }
  }

  // Read data from a characteristic
  Future<void> readCharacteristic(
      BluetoothCharacteristic characteristic) async {
    setState(() {
      statusMessage = "Reading from characteristic...";
      selectedCharacteristic = characteristic;
    });

    try {
      List<int> value = await characteristic.read();
      setState(() {
        receivedData = _dataParser(value);
        statusMessage = "Data read successfully";
      });
    } catch (e) {
      setState(() {
        statusMessage = "Failed to read: $e";
      });
    }
  }

  // Subscribe to notifications from a characteristic
  Future<void> subscribeToCharacteristic(
      BluetoothCharacteristic characteristic) async {
    // Cancel any existing subscription
    await notificationSubscription?.cancel();

    setState(() {
      statusMessage = "Subscribing to characteristic...";
      selectedCharacteristic = characteristic;
    });

    try {
      // Enable notifications
      await characteristic.setNotifyValue(true);

      // Listen for notifications
      notificationSubscription = characteristic.lastValueStream.listen((value) {
        setState(() {
          receivedData = _dataParser(value);
        });
      });

      setState(() {
        statusMessage = "Subscribed to notifications";
      });
    } catch (e) {
      setState(() {
        statusMessage = "Failed to subscribe: $e";
      });
    }
  }

  // Disconnect from device
  Future<void> disconnectDevice() async {
    if (selectedDevice == null) return;

    // Cancel notifications first
    if (notificationSubscription != null) {
      await notificationSubscription!.cancel();
      notificationSubscription = null;
    }

    // Set notify value to false if we have a characteristic
    if (selectedCharacteristic != null) {
      try {
        await selectedCharacteristic!.setNotifyValue(false);
      } catch (e) {
        debugPrint("Error disabling notifications: $e");
      }
    }

    try {
      await selectedDevice!.disconnect();
      setState(() {
        statusMessage = "Disconnected from ${selectedDevice!.platformName}";
        selectedDevice = null;
        selectedCharacteristic = null;
        receivedData = "No data received yet";
      });
    } catch (e) {
      setState(() {
        statusMessage = "Failed to disconnect: $e";
      });
    }
  }

  // Parse received data (customize based on your data format)
  String _dataParser(List<int> dataBytes) {
    // Basic conversion to string
    String result = String.fromCharCodes(dataBytes);
    // If it's not a valid string, show hex values
    if (result.contains(RegExp(r'[^\x20-\x7E]'))) {
      result = dataBytes
          .map((b) => '0x${b.toRadixString(16).padLeft(2, '0')}')
          .join(', ');
    }

    return result;
  }

  // Dialog to select service and characteristic
  void _showServiceSelectionDialog(List<BluetoothService> services) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text("Select Service and Characteristic"),
          content: SingleChildScrollView(
            child: ListBody(
              children: services.map((service) {
                return ExpansionTile(
                  title: Text('Service: ${service.uuid.toString()}'),
                  children: service.characteristics.map((characteristic) {
                    return ListTile(
                      title: Text(
                          'Characteristic: ${characteristic.uuid.toString()}'),
                      subtitle: Text(
                          'Properties: ${characteristic.properties.read ? 'Read ' : ''}${characteristic.properties.write ? 'Write ' : ''}${characteristic.properties.notify ? 'Notify ' : ''}${characteristic.properties.indicate ? 'Indicate ' : ''}'),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: <Widget>[
                          if (characteristic.properties.read)
                            IconButton(
                              icon: const Icon(Icons.file_download),
                              onPressed: () {
                                Navigator.pop(context);
                                readCharacteristic(characteristic);
                              },
                            ),
                          if (characteristic.properties.notify ||
                              characteristic.properties.indicate)
                            IconButton(
                              icon: const Icon(Icons.notifications_active),
                              onPressed: () {
                                Navigator.pop(context);
                                subscribeToCharacteristic(characteristic);
                              },
                            ),
                        ],
                      ),
                    );
                  }).toList(),
                );
              }).toList(),
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Close'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('BLE Data Reader'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Card(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Status: $statusMessage',
                        style: const TextStyle(fontWeight: FontWeight.bold)),
                    if (selectedDevice != null)
                      Text('Connected to: ${selectedDevice!.platformName}'),
                    if (selectedCharacteristic != null)
                      Text(
                          'Selected characteristic: ${selectedCharacteristic!.uuid.toString()}'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            const Text('Received Data:',
                style: TextStyle(fontWeight: FontWeight.bold)),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(8.0),
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(4.0),
                  ),
                  child: Text(receivedData),
                ),
              ),
            ),
            const SizedBox(height: 16),
            if (selectedDevice == null)
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('Available Devices:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    Expanded(
                      child: devicesList.isEmpty
                          ? const Center(child: Text('No devices found'))
                          : ListView.builder(
                              itemCount: devicesList.length,
                              itemBuilder: (context, index) {
                                return ListTile(
                                  title: Text(devicesList[index].platformName),
                                  subtitle: Text(
                                      devicesList[index].remoteId.toString()),
                                  trailing: TextButton(
                                    child: const Text('Connect'),
                                    onPressed: () =>
                                        connectToDevice(devicesList[index]),
                                  ),
                                );
                              },
                            ),
                    ),
                  ],
                ),
              ),
            if (selectedDevice != null && selectedCharacteristic == null)
              Center(
                child: ElevatedButton(
                  onPressed: discoverServices,
                  child: const Text('Discover Services'),
                ),
              ),
          ],
        ),
      ),
      floatingActionButton: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: <Widget>[
          if (selectedDevice != null)
            Padding(
              padding: const EdgeInsets.only(bottom: 16.0),
              child: FloatingActionButton(
                heroTag: 'disconnect',
                onPressed: disconnectDevice,
                backgroundColor: Colors.red,
                child: const Icon(Icons.bluetooth_disabled),
              ),
            ),
          if (selectedDevice == null)
            FloatingActionButton(
              heroTag: 'scan',
              onPressed: startScan,
              child: const Icon(Icons.bluetooth_searching),
            ),
        ],
      ),
    );
  }
}
