import 'package:flutter/services.dart';

class TemperatureInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    // Allow backspace to remove decimal point
    if (newValue.text.length < oldValue.text.length) {
      if (oldValue.text.endsWith('.') &&
          newValue.selection.baseOffset == oldValue.text.length - 1) {
        String updatedText =
            oldValue.text.substring(0, oldValue.text.length - 1);
        return TextEditingValue(
          text: updatedText,
          selection: TextSelection.collapsed(offset: updatedText.length),
        );
      }
      return newValue;
    }

    // Strip any non-digit characters
    final text = newValue.text.replaceAll(RegExp(r'[^0-9]'), '');
    StringBuffer buffer = StringBuffer();

    // Format with decimal point after 3 digits
    for (int i = 0; i < text.length; i++) {
      // Add decimal point after first 3 digits
      if (i == 3) {
        buffer.write('.');
      }
      buffer.write(text[i]);
    }

    String formatted = buffer.toString();

    // Limit to xxx.xx format (6 characters including decimal point)
    if (formatted.length > 6) {
      formatted = formatted.substring(0, 6);
    }

    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }
}
