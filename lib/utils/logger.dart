import 'package:flutter/foundation.dart';

/// A simple logger utility to standardize logging across the app
class AppLogger {
  /// Log prefixes for visual distinction
  static const String _infoPrefix = '📋';
  static const String _errorPrefix = '❌';
  static const String _successPrefix = '✅';
  static const String _warningPrefix = '⚠️';

  /// The module or component name that is using this logger
  final String module;

  /// Create a logger for a specific module or component
  AppLogger(this.module);

  /// Log an info message
  void info(String message) {
    debugPrint('$_infoPrefix $module: $message');
  }

  /// Log an error message
  void error(String message) {
    debugPrint('$_errorPrefix $module ERROR: $message');
  }

  /// Log a success message
  void success(String message) {
    debugPrint('$_successPrefix $module: $message');
  }

  /// Log a warning message
  void warning(String message) {
    debugPrint('$_warningPrefix $module WARNING: $message');
  }

  /// Creates a sub-logger with a more specific module name
  AppLogger createSubLogger(String subModule) {
    return AppLogger('$module.$subModule');
  }
}
