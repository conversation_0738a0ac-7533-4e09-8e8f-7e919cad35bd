import 'package:flutter/services.dart';

class NoEmojiInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    // This regex pattern matches emoji characters
    final RegExp emojiRegExp = RegExp(
      r'(\u00a9|\u00ae|[\u2000-\u3300]|\ud83c[\ud000-\udfff]|\ud83d[\ud000-\udfff]|\ud83e[\ud000-\udfff])',
    );

    // Replace emoji characters with empty string
    final String filteredText = newValue.text.replaceAll(emojiRegExp, '');

    // If the text is unchanged, return the original value
    if (filteredText == newValue.text) {
      return newValue;
    }

    // Calculate the new selection position based on deleted characters
    int offset = newValue.selection.baseOffset;
    if (filteredText.length < newValue.text.length) {
      offset = offset - (newValue.text.length - filteredText.length);
      offset = offset.clamp(0, filteredText.length);
    }

    // Return the filtered text with adjusted cursor position
    return TextEditingValue(
      text: filteredText,
      selection: TextSelection.collapsed(offset: offset),
    );
  }
} 