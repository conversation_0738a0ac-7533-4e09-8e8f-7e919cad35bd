import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

class DateValidator {
  /// Validates test dates to ensure they are within the last 5 years
  ///
  /// Parameters:
  /// - dateText: The date string to validate in MM/dd/yyyy format
  /// - errorField: An RxString to store any validation error message
  /// - customErrorMessages: Optional map of custom error messages
  ///
  /// Returns true if date is valid, false otherwise
  static bool validateTestDate(
    String dateText,
    RxString errorField, {
    Map<String, String>? customErrorMessages,
  }) {
    // Default error messages
    final errorMessages = {
      'empty': 'Please Enter a valid date',
      'format': 'Please Enter a valid date',
      'future': 'Please Enter a valid date',
      'range': 'Please Enter a valid date', //'Test must be from last 5 years',
    };

    // Override with custom messages if provided
    if (customErrorMessages != null) {
      errorMessages.addAll(customErrorMessages);
    }

    if (dateText.isEmpty) {
      errorField.value = errorMessages['empty']!;
      return false;
    }

    try {
      List<String> parts = dateText.split('/');
      if (parts.length != 3 || parts[2].length != 4) {
        errorField.value = errorMessages['format']!;
        return false;
      }

      DateTime date = DateFormat('MM/dd/yyyy').parseStrict(dateText);
      DateTime today = DateTime.now();

      // Check if date is in the future
      if (date.isAfter(today)) {
        errorField.value = errorMessages['future']!;
        return false;
      }

      // Check if the year is within last 5 years (including current year)
      int currentYear = today.year;
      int minYear = currentYear - 4; // 5 years including current year
      int testYear = date.year;

      if (testYear < minYear) {
        errorField.value = errorMessages['range']!;
        return false;
      }

      errorField.value = ''; // No error
      return true;
    } catch (e) {
      errorField.value = errorMessages['format']!;
      return false;
    }
  }
}
