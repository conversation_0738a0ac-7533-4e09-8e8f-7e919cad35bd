/// Helper class for consistent image name capitalization across the app
class ImageNameHelper {
  /// Ensures proper capitalization of scan image names
  /// Handles special cases like "Eye Right" -> "Right Eye" and "Eye Left" -> "Left Eye"
  static String getCapitalizedImageName(String title) {
    if (title.isEmpty) return title;
    
    switch (title.toLowerCase()) {
      case "eye right":
        return "Right Eye";
      case "eye left":
        return "Left Eye";
      case "face mesh":
        return "Face Mesh";
      case "tongue":
        return "Tongue";
      case "teeth":
        return "Teeth";
      case "palm":
        return "Palm";
      case "skin":
        return "Skin";
      default:
        // Capitalize first letter of each word for any other cases
        return title.split(' ')
            .map((word) => word.isNotEmpty 
                ? word[0].toUpperCase() + word.substring(1).toLowerCase()
                : word)
            .join(' ');
    }
  }
}
