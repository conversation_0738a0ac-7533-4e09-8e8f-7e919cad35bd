import 'package:flutter/material.dart';

import 'app_colors.dart';

ThemeData buildAppTheme() {
  return ThemeData(
    colorScheme: ColorScheme.fromSeed(
      seedColor: AppColors.black,
    ),
    fontFamily: "Arial",
    appBarTheme: AppBarTheme(
      color: Colors.white,
      shadowColor: Colors.black.withOpacity(.25),
      elevation: 2,
      centerTitle: true,
      surfaceTintColor: Colors.white,
      titleTextStyle: const TextStyle(
        fontSize: 24,
        color: Colors.black,
        fontWeight: FontWeight.w700,
      ),
      iconTheme: const IconThemeData(
        color: AppColors.gray,
      ),
    ),
    scaffoldBackgroundColor: Colors.white,
    textTheme: const TextTheme(
      bodySmall: TextStyle(color: Colors.black),
      bodyLarge: TextStyle(color: Colors.black),
      bodyMedium: TextStyle(color: Colors.black),
    ),
    useMaterial3: true,
  );
}
