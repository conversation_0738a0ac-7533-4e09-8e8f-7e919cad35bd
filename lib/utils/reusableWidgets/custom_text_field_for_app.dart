import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_text_field.dart';

class CustomTextFieldForApp extends StatelessWidget {
  const CustomTextFieldForApp({
    super.key,
    required this.textEditingController,
    required this.hintText,
    this.isRequired = true,
    this.textInputType = TextInputType.name,
    this.errorText,
    this.maxLength,
    this.inputFormatters,
    required this.title,
    this.onChanged,
    this.padding,
    this.maxLines,
    this.readOnly = false,
  });

  final TextEditingController textEditingController;
  final String hintText;
  final String title;
  final bool isRequired;
  final TextInputType textInputType;
  final String? errorText;
  final int? maxLength;
  final int? maxLines;
  final List<TextInputFormatter>? inputFormatters;
  final void Function(String)? onChanged;
  final EdgeInsetsGeometry? padding;
  final bool readOnly;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Padding(
        padding: padding ?? EdgeInsets.zero,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isRequired ? "$title*" : title,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(
                fontSize: 15,
              ),
            ),
            const SizedBox(height: 8),
            ReusableTextField(
              controller: textEditingController,
              hintText: hintText,
              keyboardType: textInputType,
              errorText: errorText,
              maxLength: maxLength,
              maxLines: maxLines,
              inputFormatters: inputFormatters,
              onChanged: onChanged,
              readOnly: readOnly,
            ),
          ],
        ),
      ),
    );
  }
}
