import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:gt_plus/global_controller.dart';
import 'package:gt_plus/modules/setting/view/setting_view.dart';
import 'package:gt_plus/utils/appConst/app_images.dart';
import '../appConst/app_colors.dart';
import 'demo_mode_overlay.dart';

AppBar reusableAppBar({
  required BuildContext context,
  bool centerTitle = true,
  List<Widget>? actions,
  double elevation = 10,
  Color backgroundColor = Colors.white,
  TextStyle? titleTextStyle,
  double? toolbarHeight = 60,
  Widget? leading,
  PreferredSizeWidget? bottom,
  bool automaticallyImplyLeading = true,
  double? leadingWidth,
  Widget? flexibleSpace,
  SystemUiOverlayStyle? systemOverlayStyle,
  IconThemeData? iconTheme,
  Size? preferredSize,
  VoidCallback? onBackPressed,
  IconData backIcon = Icons.arrow_back_ios,
  double backIconSize = 20,
  Color? backIconColor,
  double? titleSpacing,
  String? tooltip,
  bool shouldGoBack = true,
}) {
  final bool canGoBack = Navigator.canPop(context);
  final GlobalController globalController = Get.find<GlobalController>();
  final Widget backButton = IconButton(
    icon: Icon(
      backIcon,
      size: backIconSize,
      color: backIconColor ?? Theme.of(context).iconTheme.color,
    ),
    onPressed: onBackPressed ?? () => Navigator.pop(context),
  );

  final Widget saiwellLogo = Padding(
    padding: EdgeInsets.only(left: context.width * .04),
    child: Image.asset(
      AppImages.logoSaiWell,
      height: 40,
      fit: BoxFit.contain,
    ),
  );

  final Widget leadingWidget = (canGoBack && shouldGoBack)
      ? Row(
          mainAxisSize: MainAxisSize.min,
          children: [backButton, saiwellLogo],
        )
      : saiwellLogo;

  final double calculatedLeadingWidth = (canGoBack && shouldGoBack)
      ? context.width * .04 + 125 + 48
      : context.width * .04 + 125;

  final gtPlusLogo = Image.asset(
    AppImages.logoGtPlus,
    height: 44,
    fit: BoxFit.contain,
  );

  return AppBar(
      leading: leading ?? leadingWidget,
      leadingWidth: calculatedLeadingWidth,
      title: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          gtPlusLogo,
          Obx(() => Visibility(
                visible: globalController.isTestMode.value,
                child: const DemoModeOverlay(),
              )),
        ],
      ),
      centerTitle: true,
      elevation: elevation,
      backgroundColor: backgroundColor,
      titleTextStyle: titleTextStyle,
      toolbarHeight: toolbarHeight,
      surfaceTintColor: Colors.white,
      automaticallyImplyLeading: false,
      actions: actions ??
          [
            InkWell(
              onTap: () {
                Get.toNamed(SettingView.routeName);
              },
              child: Row(
                children: [
                  const Icon(
                    Icons.settings,
                    color: Colors.grey,
                  ),
                  const SizedBox(
                    width: 6,
                  ),
                  const Text(
                    "Settings",
                    style: TextStyle(
                      color: AppColors.gray,
                    ),
                  ),
                  SizedBox(
                    width: context.width * .04,
                  ),
                ],
              ),
            ),
          ]);
}
