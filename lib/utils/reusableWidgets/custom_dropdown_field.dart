import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gt_plus/utils/appConst/app_colors.dart';

import '../../models/dropdown_type_model.dart';

// Controller for CustomDropdownField
class CustomDropdownController extends GetxController {
  final TextEditingController textController;
  final List<DropdownItem> items;
  final Function(String)? onChanged;
  final bool isMultiSelect;

  // Observable state variables
  final RxString selectedValue = ''.obs;
  final RxList<String> selectedIds = <String>[].obs;

  CustomDropdownController({
    required this.textController,
    required this.items,
    this.onChanged,
    this.isMultiSelect = false,
  }) {
    // Initialize values from controller
    selectedValue.value = textController.text;
    
    // Initialize selectedIds for multi-select
    if (isMultiSelect && textController.text.isNotEmpty) {
      selectedIds.value = textController.text.split(', ');
    }
    
    // Add listener to controller for external changes
    textController.addListener(() {
      selectedValue.value = textController.text;
      if (isMultiSelect && textController.text.isNotEmpty) {
        selectedIds.value = textController.text.split(', ');
      } else if (isMultiSelect) {
        selectedIds.clear();
      }
    });
  }

  // Single select value change
  void changeSingleValue(String? value) {
    if (value != null) {
      textController.text = value;
      selectedValue.value = value;
      onChanged?.call(value);
    }
  }

  // Multi-select item toggle
  void toggleMultiSelectItem(String itemId) {
    final noneItem = items.firstWhere(
      (element) => element.name == "None",
      orElse: () => DropdownItem(id: "", name: ""),
    );
    final isNoneId = itemId == noneItem.id;

    // Check if the item is disabled (other options when None is selected)
    if (isItemDisabled(itemId)) {
      return; // Don't allow selection of disabled items
    }

    if (selectedIds.contains(itemId)) {
      // Remove item if already selected
      selectedIds.remove(itemId);
    } else {
      // If selecting "None", clear all other selections
      if (isNoneId) {
        selectedIds.clear();
      }
      selectedIds.add(itemId);
    }

    // Update controller and callback
    final newValue = selectedIds.join(', ');
    textController.text = newValue;
    selectedValue.value = newValue;
    onChanged?.call(newValue);
  }

  // Check if an item should be disabled
  bool isItemDisabled(String itemId) {
    final noneItem = items.firstWhere(
      (element) => element.name == "None",
      orElse: () => DropdownItem(id: "", name: ""),
    );
    final isNoneSelected = selectedIds.contains(noneItem.id);
    final isNoneId = itemId == noneItem.id;

    // Disable all other options when "None" is selected
    // But keep "None" itself selectable
    return isNoneSelected && !isNoneId;
  }
  
  // Get selected item names for display
  List<String> getSelectedNames() {
    return selectedIds
      .map((id) {
        final item = items.firstWhere(
          (item) => item.id == id,
          orElse: () => DropdownItem(id: "", name: ""),
        );
        return item.name;
      })
      .where((name) => name.isNotEmpty)
      .toList();
  }
  
  @override
  void onClose() {
    // No need to dispose textController as it's passed from outside
    super.onClose();
  }
}

class CustomDropdownField extends StatelessWidget {
  const CustomDropdownField({
    super.key,
    required this.textController,
    required this.hintText,
    required this.title,
    this.isRequired = true,
    required this.items,
    this.isMultiSelect = false,
    this.onChanged,
    this.errorText,
    this.readOnly = false,
  });

  final TextEditingController textController;
  final String hintText;
  final String title;
  final bool isRequired;
  final List<DropdownItem> items;
  final bool isMultiSelect;
  final Function(String)? onChanged;
  final String? errorText;
  final bool readOnly;

  @override
  Widget build(BuildContext context) {
    // Create a unique tag for this instance
    final String tag = UniqueKey().toString();
    
    // Register controller with tag
    final controller = Get.put(
      CustomDropdownController(
        textController: textController,
        items: items,
        onChanged: onChanged,
        isMultiSelect: isMultiSelect,
      ),
      tag: tag,
    );

    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isRequired ? "$title*" : title,
            style: const TextStyle(fontSize: 15),
          ),
          const SizedBox(height: 8),
          readOnly
              ? _buildReadOnlyDropdown()
              : (isMultiSelect
                  ? _buildMultiSelectDropdown(controller, tag)
                  : _buildSingleSelectDropdown(controller, tag)),
          if (errorText?.isNotEmpty == true)
            Padding(
              padding: const EdgeInsets.only(top: 4.0, left: 6),
              child: Text(
                errorText!,
                style: const TextStyle(
                  color: Colors.red,
                  fontSize: 11,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSingleSelectDropdown(CustomDropdownController controller, String tag) {
    return Obx(() => Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: errorText?.isNotEmpty == true
                  ? Colors.red
                  : AppColors.gray,
            ),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton2<String>(
              isExpanded: true,
              hint: Text(hintText,
                  style: const TextStyle(fontSize: 14, color: Colors.grey)),
              items: items.map((DropdownItem item) {
                return DropdownMenuItem<String>(
                  value: item.id,
                  child: Text(item.name, style: const TextStyle(fontSize: 14)),
                );
              }).toList(),
              value: controller.selectedValue.value.isEmpty 
                  ? null 
                  : controller.selectedValue.value,
              onChanged: controller.changeSingleValue,
              buttonStyleData: const ButtonStyleData(
                  padding: EdgeInsets.symmetric(horizontal: 16), height: 53),
              menuItemStyleData: const MenuItemStyleData(height: 40),
            ),
          ),
        ));
  }

  Widget _buildMultiSelectDropdown(CustomDropdownController controller, String tag) {
    return Obx(() {
      // Get selected names for display
      final selectedNames = controller.getSelectedNames();

      return Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: errorText?.isNotEmpty == true
                ? Colors.red
                : AppColors.gray,
          ),
        ),
        child: DropdownButtonHideUnderline(
          child: DropdownButton2<String>(
            isExpanded: true,
            // Show selected items or hint
            hint: selectedNames.isEmpty
                ? Text(
                    hintText,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  )
                : Text(
                    selectedNames.join(', '),
                    style: const TextStyle(fontSize: 14, color: Colors.black),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
            items: items.map((DropdownItem item) {
              return DropdownMenuItem<String>(
                value: item.id,
                // Prevent dropdown menu from closing when item is selected
                enabled: false,
                child: Obx(() {
                  final isSelected = controller.selectedIds.contains(item.id);
                  final isDisabled = controller.isItemDisabled(item.id);
                  return InkWell(
                    onTap: isDisabled ? null : () => controller.toggleMultiSelectItem(item.id),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Icon(
                            isSelected
                                ? Icons.check_box
                                : Icons.check_box_outline_blank,
                            color: isDisabled
                                ? Colors.grey.withValues(alpha: 0.4)
                                : (isSelected ? Colors.blue : Colors.grey),
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Flexible(
                            child: Text(
                              item.name,
                              style: TextStyle(
                                fontSize: 14,
                                color: isDisabled
                                    ? Colors.grey.withValues(alpha: 0.4)
                                    : (isSelected ? Colors.blue : Colors.black),
                              ),
                           //   overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }),
              );
            }).toList(),
            value: null,
            onChanged: (_) {},
            buttonStyleData: const ButtonStyleData(
                padding: EdgeInsets.symmetric(horizontal: 16), height: 53),
            dropdownStyleData: const DropdownStyleData(
              maxHeight: 300,
              width: 350, // Fixed width to prevent overflow
            ),
            menuItemStyleData: const MenuItemStyleData(
              height: 40,
              padding: EdgeInsets.zero,
            ),
          ),
        ),
      );
    });
  }

  Widget _buildReadOnlyDropdown() {
    // Get display text for read-only mode
    String displayText = '';
    if (isMultiSelect && textController.text.isNotEmpty) {
      final selectedIds = textController.text.split(', ');
      final selectedNames = selectedIds
          .map((id) {
            final item = items.firstWhere(
              (item) => item.id == id,
              orElse: () => DropdownItem(id: "", name: ""),
            );
            return item.name;
          })
          .where((name) => name.isNotEmpty)
          .toList();
      displayText = selectedNames.join(', ');
    } else if (!isMultiSelect && textController.text.isNotEmpty) {
      final item = items.firstWhere(
        (item) => item.id == textController.text,
        orElse: () => DropdownItem(id: "", name: ""),
      );
      displayText = item.name;
    }

    return Container(
      height: 53,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: AppColors.gray),
        color: Colors.grey.shade100,
      ),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Text(
          displayText.isEmpty ? hintText : displayText,
          style: TextStyle(
            fontSize: 14,
            color: displayText.isEmpty ? Colors.grey.shade500 : Colors.grey.shade600,
          ),
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }
}
