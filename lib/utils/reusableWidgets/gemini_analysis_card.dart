import 'package:flutter/material.dart';
import '../appConst/app_colors.dart';

/// A reusable card for Gemini AI-powered analysis features that are in beta.
/// This widget can be used for different report types like Audiogram and Cognivue.
class GeminiAnalysisCard extends StatelessWidget {
  /// The title of the AI analysis feature
  final String title;
  
  /// The type of report (e.g., "audiogram", "cognivue")
  final String reportType;
  
  /// Function to be called when the card is tapped
  final VoidCallback onTap;

  const GeminiAnalysisCard({
    super.key,
    required this.title,
    required this.reportType,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.orange.shade50,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.deepOrange.shade300),
        ),
        child: Row(
          children: [
            Icon(
              Icons.assistant,
              color: Colors.deepOrange.shade400,
              size: 28,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '$title (Beta)',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.charcoalBlue,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    "We're introducing a new AI-powered feature for $reportType report data extraction.\nNote: This is a beta version and may not always provide accurate results. Kindly verify all extracted values manually before submission.",
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade700,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: Colors.grey.shade600,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }
} 