import 'package:flutter/material.dart';
import '../../models/medical_report_model.dart';
import '../../modules/gemini_image_to_text/gemini_report_card.dart';


/// Shows a bottom sheet with the Gemini analysis tool for different report types
void showGeminiBottomSheet({
  required BuildContext context,
  required String title,
  required ReportType reportType,
  required String toolTitle,
  required String description,
  required Function(MedicalReportModel) onDataExtracted,
}) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.white,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
    ),
    builder: (context) => Padding(
      padding: EdgeInsets.only(
        top: 16,
        left: 16, 
        right: 16,
        bottom: MediaQuery.of(context).viewInsets.bottom + 16,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GeminiReportCard(
            reportType: reportType,
            toolTitle: toolTitle,
            description: description,
            onDataExtracted: onDataExtracted,
          ),
          const SizedBox(height: 16),
        ],
      ),
    ),
  );
} 