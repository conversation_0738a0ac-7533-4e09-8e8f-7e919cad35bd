import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../modules/scanImages/controller/scan_images_controller.dart';
import '../appConst/app_images.dart';
import '../helpers/image_name_helper.dart';

class ReusableScanImagesMainBox extends StatelessWidget {
  const ReusableScanImagesMainBox(
      {super.key, required this.scanImageDataList, required this.index});

  final List<ScanImageDataModel> scanImageDataList;
  final int index;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(right: 24),
      child: InkWell(
        onTap: scanImageDataList[index].onTap,
        child: Stack(
          alignment: Alignment.bottomLeft,
          children: [
            Image.asset(
              scanImageDataList[index].image,
              fit: BoxFit.cover,
              width: Get.width * .32 - 72,
              height: Get.width * .32 - 72,
            ),
            ClipRect(
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 14, vertical: 6),
                  decoration: BoxDecoration(
                      color: Colors.grey.shade500.withValues(alpha: 0.5)),
                  child: Text(
                    ImageNameHelper.getCapitalizedImageName(scanImageDataList[index].title),
                    style: const TextStyle(
                      fontSize: 15,
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ),
            Positioned(
                top: 0,
                left: 0,
                child: scanImageDataList[index].isCompleted
                    ? SvgPicture.asset(
                        AppImages.icCheckCircularBlue,
                        height: 32,
                        width: 32,
                      )
                    : const SizedBox.shrink()),
          ],
        ),
      ),
    );
  }
}
