import 'package:flutter/material.dart';

class ReusableButton extends StatelessWidget {
  final String title;
  final bool isLoading;
  final bool isDisabled;
  final Color? color;
  final Color? borderColor;
  final Color? fontColor;
  final double height;
  final double width;
  final double radius;
  final double fontSize;
  final double elevation;
  final Widget? prefixIcon; // Added prefix icon parameter
  final Widget? suffixIcon; // Added suffix icon parameter

  final void Function()? onTap;

  const ReusableButton({
    super.key,
    required this.title,
    this.isLoading = false,
    this.isDisabled = false,
    this.color,
    this.fontSize = 24,
    required this.onTap,
    this.height = 54,
    this.width = double.infinity,
    this.radius = 4,
    this.borderColor,
    this.elevation = 0,
    this.fontColor,
    this.prefixIcon,
    this.suffixIcon,
  });

  @override
  Widget build(BuildContext context) {
    // Check if button should be inactive (disabled or loading)
    final bool isInactive = isDisabled || isLoading || onTap == null;

    return InkWell(
      onTap: isInactive ? null : onTap,
      child: Opacity(
        opacity: isInactive ? .5 : 1,
        child: Material(
          elevation: elevation,
          borderRadius: BorderRadius.circular(radius),
          child: Container(
            height: height,
            width: width,
            decoration: BoxDecoration(
                color:
                    isDisabled ? Colors.grey : color ?? const Color(0xffF26511),
                borderRadius: BorderRadius.circular(radius),
                border: Border.all(
                    color: isDisabled
                        ? Colors.grey
                        : borderColor ?? const Color(0xffF26511),
                    width: 1)),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: Center(
                child: isLoading
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2.5,
                          color: Colors.white,
                        ),
                      )
                    : Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          if (prefixIcon != null) ...[
                            prefixIcon!,
                            const SizedBox(width: 8),
                          ],
                          FittedBox(
                            fit: BoxFit.scaleDown,
                            child: Text(
                              title,
                              style: TextStyle(
                                  fontSize: fontSize,
                                  color: isDisabled
                                      ? Colors.white
                                      : fontColor ??
                                          (color == Colors.white
                                              ? Colors.black
                                              : Colors.white),
                                  letterSpacing: .5),
                            ),
                          ),
                          if (suffixIcon != null) ...[
                            const SizedBox(width: 8),
                            suffixIcon!,
                          ],
                        ],
                      ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
