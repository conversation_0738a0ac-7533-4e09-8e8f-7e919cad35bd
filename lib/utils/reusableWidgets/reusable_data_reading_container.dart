import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../appConst/app_colors.dart';

class ReusableDataReadingContainer extends StatelessWidget {
  const ReusableDataReadingContainer({
    super.key,
    required this.dataType,
    required this.dataValue,
    required this.dataUnit,
    required this.icon,
  });

  final String dataType;
  final String dataValue;
  final String dataUnit;
  final String icon;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 80,
      width: context.width * .34,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.softGray),
        borderRadius: BorderRadius.circular(15),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SvgPicture.asset(icon),
          const SizedBox(
            width: 12,
          ),
          Text(
            dataType,
            style: const TextStyle(
              fontSize: 18,
              color: AppColors.gray,
            ),
          ),
          const Spacer(),
          Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.baseline,
            textBaseline: TextBaseline.alphabetic,
            children: [
              Text(
                dataValue,
                style: const TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(
                width: 2,
              ),
              Text(
                dataUnit,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.normal,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
