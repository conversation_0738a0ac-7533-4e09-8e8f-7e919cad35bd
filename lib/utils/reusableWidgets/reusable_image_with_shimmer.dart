import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../appConst/app_images.dart';
import 'common_app_shimmer.dart';

class ReusableImageWithShimmer extends StatelessWidget {
  const ReusableImageWithShimmer({
    super.key,
    required this.url,
    required this.height,
    this.onTap,
    this.isCircle = true,
    this.boxFit = BoxFit.cover,
    this.placeholderUrl = 'asset/images/no_image_available.svg',
    this.isBackDrop = false,
    this.borderRadius = 0.0,
    this.width,
    this.isProfile = false,
  });
  final double height;
  final String url;
  final bool isCircle;
  final bool isBackDrop;
  final BoxFit boxFit;
  final String placeholderUrl;
  final void Function()? onTap;
  final double borderRadius;
  final bool isProfile;
  final double? width;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: ClipRRect(
        borderRadius:
            BorderRadius.circular(isCircle ? height / 2 : borderRadius),
        child: SizedBox(
          height: height,
          width: width ?? height,
          child: url.isNotEmpty
              ? CachedNetworkImage(
                  imageUrl: url,
                  errorWidget: (context, url, error) {
                    return ClipRRect(
                      borderRadius: BorderRadius.circular(
                          isCircle ? height / 2 : borderRadius),
                      child: SvgPicture.asset(
                        isProfile
                            ? AppImages.imgNoImage
                            : placeholderUrl,
                        fit: BoxFit.cover,
                      ),
                    );
                  },
                  fit: boxFit,
                  placeholder: (context, url) {
                    return isCircle
                        ? CommonAppShimmer.circular(height: height)
                        : CommonAppShimmer.rectangular(height: height);
                  },
                )
              : SvgPicture.asset(
                  isProfile ? AppImages.imgNoImage : placeholderUrl,
                  fit: BoxFit.cover,
                ),
        ),
      ),
    );
  }
}
