import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:async';

late SnackbarController? _currentSnackbar;
Timer? _debounceTimer;

void reusableSnackBar({
  required String message,
  int duration = 2,
  bool isForError = true,
  bool isInfinite = false,
}) {
  _debounceTimer?.cancel();

  void closeSnackBar() {
    if (Get.isSnackbarOpen && _currentSnackbar != null) {
      _currentSnackbar?.close();
      _currentSnackbar = null;
    }
  }

  closeSnackBar();

  _debounceTimer = Timer(const Duration(milliseconds: 250), () {
    _currentSnackbar = Get.showSnackbar(
      GetSnackBar(
        borderRadius: 12,
        snackStyle: SnackStyle.FLOATING,
        duration: isInfinite ? null : Duration(seconds: duration),
        backgroundColor: isForError ? Colors.red : Colors.green.shade400,
        titleText: Text(
          isForError ? "Error" : "Success",
          style: const TextStyle(
              color: Colors.white, fontWeight: FontWeight.w700, fontSize: 17),
        ),
        messageText: Text(
          message,
          style: const TextStyle(color: Colors.white, fontSize: 14),
        ),
        margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
      ),
    );
  });
}
