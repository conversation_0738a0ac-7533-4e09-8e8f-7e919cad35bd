import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../modules/ppg/view/ppg_waveform.dart';
import '../appConst/app_colors.dart';

class ReusableFooter extends StatelessWidget {
  const ReusableFooter({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: context.height * .17,
      decoration: const BoxDecoration(
        color: AppColors.darkTeal,
      ),
      child:   const PPGWaveform(),
    );
  }
}
