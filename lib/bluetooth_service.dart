import 'dart:async';
import 'package:flutter/services.dart';

class BluetoothService {
  static final BluetoothService _instance = BluetoothService._internal();
  factory BluetoothService() => _instance;
  BluetoothService._internal();

  final MethodChannel _channel =
      const MethodChannel('com.telehealth.bluetooth/channel');

  // Stream controllers for events
  final _deviceFoundController =
      StreamController<Map<String, dynamic>>.broadcast();
  final _bluetoothStatusController = StreamController<int>.broadcast();
  final _connectionStateController =
      StreamController<Map<String, dynamic>>.broadcast();
  final _pairingStateController =
      StreamController<Map<String, dynamic>>.broadcast();
  final _bpDataController = StreamController<Map<String, dynamic>>.broadcast();
  final _activityDataController =
      StreamController<Map<String, dynamic>>.broadcast();
  final _glucoseDataController =
      StreamController<Map<String, dynamic>>.broadcast();
  final _pairMessageController =
      StreamController<Map<String, dynamic>>.broadcast();
  final _pulseOxDataController =
      StreamController<Map<String, dynamic>>.broadcast();
  final _pulseWaveDataController =
  StreamController<Map<String, dynamic>>.broadcast();

  Stream<Map<String, dynamic>> get onActivityData =>
      _activityDataController.stream;
  Stream<Map<String, dynamic>> get onGlucoseData =>
      _glucoseDataController.stream;
  Stream<Map<String, dynamic>> get onPairMessage =>
      _pairMessageController.stream;

  // Streams
  Stream<Map<String, dynamic>> get onDeviceFound =>
      _deviceFoundController.stream;
  Stream<int> get onBluetoothStatusChanged => _bluetoothStatusController.stream;
  Stream<Map<String, dynamic>> get onConnectionStateChanged =>
      _connectionStateController.stream;
  Stream<Map<String, dynamic>> get onPairingStateChanged =>
      _pairingStateController.stream;
  Stream<Map<String, dynamic>> get onBPDataReceived => _bpDataController.stream;
  Stream<Map<String, dynamic>> get onPulseOxData =>
      _pulseOxDataController.stream;
  Stream<Map<String, dynamic>> get onPulseWaveData =>
      _pulseWaveDataController.stream;

  // Initialize service and set up method channel callbacks
  Future<void> initialize() async {
    _channel.setMethodCallHandler(_handleMethodCall);
    await _channel.invokeMethod('getCurrentBluetoothState');
  }

  // Method call handler for incoming events from native
  Future<dynamic> _handleMethodCall(MethodCall call) async {
    switch (call.method) {
      case 'deviceFound':
        final Map<String, dynamic> device =
            Map<String, dynamic>.from(call.arguments);
        _deviceFoundController.add(device);
        break;
      case 'bluetoothStatus':
        final Map<String, dynamic> args =
            Map<String, dynamic>.from(call.arguments);
        _bluetoothStatusController.add(args['state']);
        break;
      case 'systemBluetoothStatus':
        final Map<String, dynamic> args =
            Map<String, dynamic>.from(call.arguments);
        _bluetoothStatusController.add(args['state']);
        break;
      case 'connectionStateChanged':
        final Map<String, dynamic> state =
            Map<String, dynamic>.from(call.arguments);
        _connectionStateController.add(state);
        break;
      case 'pairingStatus':
        final Map<String, dynamic> state =
            Map<String, dynamic>.from(call.arguments);
        _pairingStateController.add(state);
        break;
      case 'bpData':
        final Map<String, dynamic> data =
            Map<String, dynamic>.from(call.arguments);
        print("added bp data in flutter $data");
        _bpDataController.add(data);
        break;
      case 'activityData':
        final data = Map<String, dynamic>.from(call.arguments);
        _activityDataController.add(data);
        break;
      case 'glucoseData':
        final data = Map<String, dynamic>.from(call.arguments);
        _glucoseDataController.add(data);
        break;
      case 'pairMessageUpdate':
        final data = Map<String, dynamic>.from(call.arguments);
        _pairMessageController.add(data);
        break;
      case 'pulseOxData':
        final data = Map<String, dynamic>.from(call.arguments);
        _pulseOxDataController.add(data);
        break;
      case 'pulseWaveData':
        final data = Map<String, dynamic>.from(call.arguments);
        _pulseWaveDataController.add(data);
        break;
    }
    return null;
  }

  // Methods to call native code
  Future<void> startScan() async {
    await _channel.invokeMethod('startScan');
  }

  Future<void> stopScan() async {
    await _channel.invokeMethod('stopScan');
  }

  Future<bool> pairDevice(Map<String, dynamic> device) async {
    try {
      bool isPaired = await _channel.invokeMethod('pairDevice', {
        'mac': device['mac'],
        'broadcastId': device['broadcastId'],
        'type': device['type'],
        'protocolType': device['protocolType'],
      });
      print("pairDevice : $isPaired");
      return isPaired;
    } on PlatformException catch (e) {
      print("pairDevice error: ${e.toString()}");
      return false;
    }
  }

  Future<void> startDataSync({required String deviceType}) async {
    await _channel.invokeMethod('startDataSync');
  }

  Future<int> getCurrentBluetoothState() async {
    int state = await _channel.invokeMethod('getCurrentBluetoothState');
    return state;
  }

  Future<List<Map<String, dynamic>>> getFoundDevices() async {
    final List<dynamic> result = await _channel.invokeMethod('getFoundDevices');
    return result.map((device) => Map<String, dynamic>.from(device)).toList();
  }

  // Dispose streams when done
  void dispose() {
    _deviceFoundController.close();
    _bluetoothStatusController.close();
    _connectionStateController.close();
    _pairingStateController.close();
    _bpDataController.close();
  }
}
