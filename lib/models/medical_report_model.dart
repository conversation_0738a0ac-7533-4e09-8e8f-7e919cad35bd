/// Base class for all medical report models
abstract class MedicalReportModel {
  final String testDate;
  final String created;
  final String lastUpdated;

  MedicalReportModel({
    required this.testDate,
    required this.created,
    required this.lastUpdated,
  });

  /// Convert the model to JSON
  Map<String, dynamic> toJson();

  /// Check if the report has any valid data
  bool get hasValidData;
} 