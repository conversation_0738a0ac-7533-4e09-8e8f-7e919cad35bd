import 'medical_report_model.dart';

class AudiometryDetailsModel extends MedicalReportModel {
  final String left250;
  final String left500;
  final String left1000;
  final String left1500;
  final String left2000;
  final String left3000;
  final String left4000;
  final String left6000;
  final String left8000;
  final String leftPP;
  final String leftWRS;
  
  final String right250;
  final String right500;
  final String right1000;
  final String right1500;
  final String right2000;
  final String right3000;
  final String right4000;
  final String right6000;
  final String right8000;
  final String rightPP;
  final String rightWRS;
  
  AudiometryDetailsModel({
    required this.left250,
    required this.left500,
    required this.left1000,
    this.left1500 = '',
    required this.left2000,
    this.left3000 = '',
    required this.left4000,
    this.left6000 = '',
    required this.left8000,
    required this.leftPP,
    required this.leftWRS,
    required this.right250,
    required this.right500,
    required this.right1000,
    this.right1500 = '',
    required this.right2000,
    this.right3000 = '',
    required this.right4000,
    this.right6000 = '',
    required this.right8000,
    required this.rightPP,
    required this.rightWRS,
    required String testDate,
    required String created,
    required String lastUpdated,
  }) : super(
          testDate: testDate,
          created: created,
          lastUpdated: lastUpdated,
        );

  factory AudiometryDetailsModel.fromJson(Map<String, dynamic> json) {
    return AudiometryDetailsModel(
      left250: json['Left250']?.toString() ?? '',
      left500: json['Left500']?.toString() ?? '',
      left1000: json['Left1000']?.toString() ?? '',
      left1500: json['Left1500']?.toString() ?? '',
      left2000: json['Left2000']?.toString() ?? '',
      left3000: json['Left3000']?.toString() ?? '',
      left4000: json['Left4000']?.toString() ?? '',
      left6000: json['Left6000']?.toString() ?? '',
      left8000: json['Left8000']?.toString() ?? '',
      leftPP: json['LeftPP']?.toString() ?? '',
      leftWRS: json['LeftWRS']?.toString() ?? '',
      right250: json['Right250']?.toString() ?? '',
      right500: json['Right500']?.toString() ?? '',
      right1000: json['Right1000']?.toString() ?? '',
      right1500: json['Right1500']?.toString() ?? '',
      right2000: json['Right2000']?.toString() ?? '',
      right3000: json['Right3000']?.toString() ?? '',
      right4000: json['Right4000']?.toString() ?? '',
      right6000: json['Right6000']?.toString() ?? '',
      right8000: json['Right8000']?.toString() ?? '',
      rightPP: json['RightPP']?.toString() ?? '',
      rightWRS: json['RightWRS']?.toString() ?? '',
      testDate: json['TestDate']?.toString() ?? '',
      created: json['created'] ?? '',
      lastUpdated: json['lastUpdated'] ?? '',
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'Left250': left250,
      'Left500': left500,
      'Left1000': left1000,
      'Left1500': left1500,
      'Left2000': left2000,
      'Left3000': left3000,
      'Left4000': left4000,
      'Left6000': left6000,
      'Left8000': left8000,
      'LeftPP': leftPP,
      'LeftWRS': leftWRS,
      'Right250': right250,
      'Right500': right500,
      'Right1000': right1000,
      'Right1500': right1500,
      'Right2000': right2000,
      'Right3000': right3000,
      'Right4000': right4000,
      'Right6000': right6000,
      'Right8000': right8000,
      'RightPP': rightPP,
      'RightWRS': rightWRS,
      'TestDate': testDate,
      'created': created,
      'lastUpdated': lastUpdated,
    };
  }
  
  @override
  bool get hasValidData =>
    left250.isNotEmpty || 
    left500.isNotEmpty || 
    left1000.isNotEmpty || 
    left1500.isNotEmpty || 
    left2000.isNotEmpty ||
    left3000.isNotEmpty ||
    left4000.isNotEmpty || 
    left6000.isNotEmpty ||
    left8000.isNotEmpty ||
    leftWRS.isNotEmpty ||
    right250.isNotEmpty || 
    right500.isNotEmpty || 
    right1000.isNotEmpty || 
    right1500.isNotEmpty ||
    right2000.isNotEmpty ||
    right3000.isNotEmpty ||
    right4000.isNotEmpty || 
    right6000.isNotEmpty ||
    right8000.isNotEmpty ||
    rightWRS.isNotEmpty;
} 