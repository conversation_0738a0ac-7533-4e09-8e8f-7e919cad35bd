class GTInstructionsModel {
  List<String>? ppg;
  List<String>? temperature;
  List<String>? bp;

  GTInstructionsModel({this.ppg, this.temperature, this.bp});

  GTInstructionsModel.fromJson(Map<String, dynamic> json) {
    ppg = json['ppg'] != null ? List<String>.from(json['ppg']) : null;
    temperature = json['temperature'] != null ? List<String>.from(json['temperature']) : null;
    bp = json['BP'] != null ? List<String>.from(json['BP']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['ppg'] = ppg;
    data['temperature'] = temperature;
    data['bp'] = bp;
    return data;
  }
}
