class ExtraPatientDataModel {
  final List<ExtraPatientDataItem> items;

  ExtraPatientDataModel({required this.items});

  factory ExtraPatientDataModel.fromJson(Map<String, dynamic> json) {
    List<ExtraPatientDataItem> items = [];
    
    if (json.containsKey('items') && json['items'] is List) {
      items = (json['items'] as List)
          .map((item) => ExtraPatientDataItem.fromJson(item))
          .toList();
    }

    return ExtraPatientDataModel(items: items);
  }

  Map<String, dynamic> toJson() {
    return {
      'items': items.map((item) => item.toJson()).toList(),
    };
  }
}

class ExtraPatientDataItem {
  final String name;
  final String value;

  ExtraPatientDataItem({required this.name, required this.value});

  factory ExtraPatientDataItem.fromJson(Map<String, dynamic> json) {
    return ExtraPatientDataItem(
      name: json['name'] ?? '',
      value: json['value'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'value': value,
    };
  }
} 