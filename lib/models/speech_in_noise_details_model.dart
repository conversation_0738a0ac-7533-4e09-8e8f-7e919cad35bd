import 'medical_report_model.dart';

class SpeechInNoiseDetailsModel extends MedicalReportModel {
  final String leftEarSnrLoss;
  final String rightEarSnrLoss;

  SpeechInNoiseDetailsModel({
    required this.leftEarSnrLoss,
    required this.rightEarSnrLoss,
    required String testDate,
    required String created,
    required String lastUpdated,
  }) : super(
          testDate: testDate,
          created: created,
          lastUpdated: lastUpdated,
        );

  /// Extracts numeric value from strings that may contain units like "dB"
  /// Examples: "5dB" -> "5", "3.5 dB" -> "3.5", "2" -> "2"
  static String _extractNumericValue(String value) {
    if (value.isEmpty) return '';

    // Remove any whitespace
    String cleanValue = value.trim();

    // For decibel values, extract the number before "db", "dB", "DB"
    final dbRegex = RegExp(r'^(-?\d+(?:\.\d+)?)\s*(?:db|dB|DB)?$', caseSensitive: false);
    final match = dbRegex.firstMatch(cleanValue);
    if (match != null && match.groupCount >= 1) {
      return match.group(1) ?? '';
    }

    // If it's already just a number (with optional negative sign and decimal)
    final numericRegex = RegExp(r'^-?\d+(?:\.\d+)?$');
    if (numericRegex.hasMatch(cleanValue)) {
      return cleanValue;
    }

    // If we can't extract anything meaningful, return the original value
    return cleanValue;
  }

  factory SpeechInNoiseDetailsModel.fromJson(Map<String, dynamic> json) {
    return SpeechInNoiseDetailsModel(
      leftEarSnrLoss: _extractNumericValue(json['LeftEarSnrLoss']?.toString() ?? ''),
      rightEarSnrLoss: _extractNumericValue(json['RightEarSnrLoss']?.toString() ?? ''),
      testDate: json['TestDate']?.toString() ?? '',
      created: json['created'] ?? '',
      lastUpdated: json['lastUpdated'] ?? '',
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'LeftEarSnrLoss': leftEarSnrLoss,
      'RightEarSnrLoss': rightEarSnrLoss,
      'TestDate': testDate,
      'created': created,
      'lastUpdated': lastUpdated,
    };
  }
  
  @override
  bool get hasValidData => 
    leftEarSnrLoss.isNotEmpty || 
    rightEarSnrLoss.isNotEmpty;
} 