import 'medical_report_model.dart';

class CognivueDetailsModel extends MedicalReportModel {
  final String executiveFunction;
  final String memory;
  final String processingSpeed;
  final String reactionTime;
  final String visuoSpatial;

  CognivueDetailsModel({
    required this.executiveFunction,
    required this.memory,
    required this.processingSpeed,
    required this.reactionTime,
    required String testDate,
    required this.visuoSpatial,
    required String created,
    required String lastUpdated,
  }) : super(
          testDate: testDate,
          created: created,
          lastUpdated: lastUpdated,
        );

  /// Extracts numeric value from strings that may contain units
  /// Examples: "1.5ms" -> "1.5", "85%" -> "85", "42" -> "42"
  static String _extractNumericValue(String value) {
    if (value.isEmpty) return '';

    // Remove any whitespace
    String cleanValue = value.trim();

    // For percentage values, extract the number before %
    if (cleanValue.contains('%')) {
      final percentRegex = RegExp(r'(\d+(?:\.\d+)?)%');
      final match = percentRegex.firstMatch(cleanValue);
      if (match != null && match.groupCount >= 1) {
        return match.group(1) ?? '';
      }
    }

    // For time values (ms, seconds, etc.), extract the number before the unit
    final timeRegex = RegExp(r'^(\d+(?:\.\d+)?)\s*(?:ms|sec|seconds?|s)?$', caseSensitive: false);
    final match = timeRegex.firstMatch(cleanValue);
    if (match != null && match.groupCount >= 1) {
      return match.group(1) ?? '';
    }

    // If it's already just a number (with optional decimal)
    final numericRegex = RegExp(r'^\d+(?:\.\d+)?$');
    if (numericRegex.hasMatch(cleanValue)) {
      return cleanValue;
    }

    // If we can't extract anything meaningful, return the original value
    return cleanValue;
  }

  factory CognivueDetailsModel.fromJson(Map<String, dynamic> json) {
    return CognivueDetailsModel(
      executiveFunction: _extractNumericValue(json['ExecutiveFunction']?.toString() ?? ''),
      memory: _extractNumericValue(json['Memory']?.toString() ?? ''),
      processingSpeed: _extractNumericValue(json['ProcessingSpeed']?.toString() ?? ''),
      reactionTime: _extractNumericValue(json['ReactionTime']?.toString() ?? ''),
      testDate: json['TestDate']?.toString() ?? '',
      visuoSpatial: _extractNumericValue(json['VisuoSpatial']?.toString() ?? ''),
      created: json['created'] ?? '',
      lastUpdated: json['lastUpdated'] ?? '',
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'ExecutiveFunction': executiveFunction,
      'Memory': memory,
      'ProcessingSpeed': processingSpeed,
      'ReactionTime': reactionTime,
      'TestDate': testDate,
      'VisuoSpatial': visuoSpatial,
      'created': created,
      'lastUpdated': lastUpdated,
    };
  }
  
  @override
  bool get hasValidData => 
    memory.isNotEmpty || 
    visuoSpatial.isNotEmpty || 
    executiveFunction.isNotEmpty || 
    reactionTime.isNotEmpty || 
    processingSpeed.isNotEmpty;
} 