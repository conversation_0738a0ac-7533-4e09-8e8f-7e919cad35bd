import 'dart:convert';
import 'package:flutter/services.dart';

import '../enums/dropdown_type_enum.dart';

Future<List<Map<String, dynamic>>> loadJsonData(DropdownTypesEnum type) async {
  String jsonString = await rootBundle.loadString(type.path);
  List<dynamic> jsonResponse = json.decode(jsonString);
  return jsonResponse.map((item) => Map<String, dynamic>.from(item)).toList();
}

class DropdownItem {
  final String id;
  final String name;

  DropdownItem({required this.id, required this.name});

  factory DropdownItem.fromJson(Map<String, dynamic> json) {
    return DropdownItem(
      id: json['ID'].toString(),
      name: json['Name'].toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'ID': id,
      'Name': name,
    };
  }

  @override
  String toString() => name;
}