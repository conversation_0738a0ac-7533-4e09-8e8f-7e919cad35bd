import 'audiometry_details_model.dart';

class FrequencyValues {
  final String f250;
  final String f500;
  final String f1k;
  final String f1_5k;
  final String f2k;
  final String f3k;
  final String f4k;
  final String f6k;
  final String f8k;

  FrequencyValues({
    required this.f250,
    required this.f500,
    required this.f1k,
    this.f1_5k = '',
    required this.f2k,
    this.f3k = '',
    required this.f4k,
    this.f6k = '',
    required this.f8k,
  });

  factory FrequencyValues.fromJson(Map<String, dynamic> json) {
    return FrequencyValues(
      f250: json['f250'] ?? '',
      f500: json['f500'] ?? '',
      f1k: json['f1k'] ?? '',
      f1_5k: json['f1.5k'] ?? '',
      f2k: json['f2k'] ?? '',
      f3k: json['f3k'] ?? '',
      f4k: json['f4k'] ?? '',
      f6k: json['f6k'] ?? '',
      f8k: json['f8k'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'f250': f250,
      'f500': f500,
      'f1k': f1k,
      'f1.5k': f1_5k,
      'f2k': f2k,
      'f3k': f3k,
      'f4k': f4k,
      'f6k': f6k,
      'f8k': f8k,
    };
  }
}

class EarData {
  final List<FrequencyValues> ac;

  EarData({
    required this.ac,
  });

  factory EarData.fromJson(Map<String, dynamic> json) {
    List<dynamic> acList = json['AC'] ?? [];
    return EarData(
      ac: acList.map((item) => FrequencyValues.fromJson(item)).toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'AC': ac.map((item) => item.toJson()).toList(),
    };
  }
}

class SRTWRSData {
  final String wrs;

  SRTWRSData({
    required this.wrs,
  });

  factory SRTWRSData.fromJson(Map<String, dynamic> json) {
    return SRTWRSData(
      wrs: json['WRS'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'WRS': wrs,
    };
  }
}

class Tympanometry {
  final String peakPressure;

  Tympanometry({
    required this.peakPressure,
  });

  factory Tympanometry.fromJson(Map<String, dynamic> json) {
    return Tympanometry(
      peakPressure: json['peakpressaure'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'peakpressaure': peakPressure,
    };
  }
}

class OtherTableData {
  final List<Tympanometry> left;
  final List<Tympanometry> right;

  OtherTableData({
    required this.left,
    required this.right,
  });

  factory OtherTableData.fromJson(Map<String, dynamic> json) {
    List<dynamic> leftList = json['left'] ?? [];
    List<dynamic> rightList = json['right'] ?? [];
    
    return OtherTableData(
      left: leftList.map((item) => Tympanometry.fromJson(item)).toList(),
      right: rightList.map((item) => Tympanometry.fromJson(item)).toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'left': left.map((item) => item.toJson()).toList(),
      'right': right.map((item) => item.toJson()).toList(),
    };
  }
}

class GeminiAudiogramModel {
  final List<EarData> tableRight;
  final List<EarData> tableLeft;
  final List<SRTWRSData> srtRight;
  final List<SRTWRSData> srtLeft;
  final OtherTableData table;

  GeminiAudiogramModel({
    required this.tableRight,
    required this.tableLeft,
    required this.srtRight,
    required this.srtLeft,
    required this.table,
  });

  factory GeminiAudiogramModel.fromJson(Map<String, dynamic> json) {
    List<dynamic> tableRightList = json['tableright'] ?? [];
    List<dynamic> tableLeftList = json['table_left'] ?? [];
    List<dynamic> srtRightList = json['srtright'] ?? [];
    List<dynamic> srtLeftList = json['srtleft'] ?? [];

    return GeminiAudiogramModel(
      tableRight: tableRightList.map((item) => EarData.fromJson(item)).toList(),
      tableLeft: tableLeftList.map((item) => EarData.fromJson(item)).toList(),
      srtRight: srtRightList.map((item) => SRTWRSData.fromJson(item)).toList(),
      srtLeft: srtLeftList.map((item) => SRTWRSData.fromJson(item)).toList(),
      table: OtherTableData.fromJson(json['table'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'tableright': tableRight.map((item) => item.toJson()).toList(),
      'table_left': tableLeft.map((item) => item.toJson()).toList(),
      'srtright': srtRight.map((item) => item.toJson()).toList(),
      'srtleft': srtLeft.map((item) => item.toJson()).toList(),
      'table': table.toJson(),
    };
  }

  /// Extracts numeric value from strings that may contain units like "db", "dB", "%", etc.
  /// Examples: "60db" -> "60", "65DB" -> "65", "44% at 95db" -> "44", "60" -> "60"
  String _extractNumericValue(String value) {
    if (value.isEmpty) return '';

    // Remove any whitespace
    String cleanValue = value.trim();

    // For percentage values (like WRS), extract the number before %
    if (cleanValue.contains('%')) {
      final percentRegex = RegExp(r'(\d+(?:\.\d+)?)%');
      final match = percentRegex.firstMatch(cleanValue);
      if (match != null && match.groupCount >= 1) {
        return match.group(1) ?? '';
      }
    }

    // For decibel values, extract the number before "db", "dB", "DB"
    final dbRegex = RegExp(r'^(-?\d+(?:\.\d+)?)\s*(?:db|dB|DB)?$', caseSensitive: false);
    final match = dbRegex.firstMatch(cleanValue);
    if (match != null && match.groupCount >= 1) {
      return match.group(1) ?? '';
    }

    // If it's already just a number (with optional negative sign and decimal)
    final numericRegex = RegExp(r'^-?\d+(?:\.\d+)?$');
    if (numericRegex.hasMatch(cleanValue)) {
      return cleanValue;
    }

    // If we can't extract anything meaningful, return the original value
    return cleanValue;
  }

  AudiometryDetailsModel toAudiometryDetailsModel() {
    String leftF250 = '';
    String leftF500 = '';
    String leftF1k = '';
    String leftF1_5k = '';
    String leftF2k = '';
    String leftF3k = '';
    String leftF4k = '';
    String leftF6k = '';
    String leftF8k = '';
    String leftPP = '';
    String leftWRS = '';

    String rightF250 = '';
    String rightF500 = '';
    String rightF1k = '';
    String rightF1_5k = '';
    String rightF2k = '';
    String rightF3k = '';
    String rightF4k = '';
    String rightF6k = '';
    String rightF8k = '';
    String rightPP = '';
    String rightWRS = '';

    // Extract left ear data with numeric value extraction
    if (tableLeft.isNotEmpty && tableLeft[0].ac.isNotEmpty) {
      var leftData = tableLeft[0].ac[0];
      leftF250 = _extractNumericValue(leftData.f250);
      leftF500 = _extractNumericValue(leftData.f500);
      leftF1k = _extractNumericValue(leftData.f1k);
      leftF1_5k = _extractNumericValue(leftData.f1_5k);
      leftF2k = _extractNumericValue(leftData.f2k);
      leftF3k = _extractNumericValue(leftData.f3k);
      leftF4k = _extractNumericValue(leftData.f4k);
      leftF6k = _extractNumericValue(leftData.f6k);
      leftF8k = _extractNumericValue(leftData.f8k);
    }

    // Extract right ear data with numeric value extraction
    if (tableRight.isNotEmpty && tableRight[0].ac.isNotEmpty) {
      var rightData = tableRight[0].ac[0];
      rightF250 = _extractNumericValue(rightData.f250);
      rightF500 = _extractNumericValue(rightData.f500);
      rightF1k = _extractNumericValue(rightData.f1k);
      rightF1_5k = _extractNumericValue(rightData.f1_5k);
      rightF2k = _extractNumericValue(rightData.f2k);
      rightF3k = _extractNumericValue(rightData.f3k);
      rightF4k = _extractNumericValue(rightData.f4k);
      rightF6k = _extractNumericValue(rightData.f6k);
      rightF8k = _extractNumericValue(rightData.f8k);
    }

    // Extract WRS data with numeric value extraction
    if (srtLeft.isNotEmpty) {
      leftWRS = _extractNumericValue(srtLeft[0].wrs);
    }
    if (srtRight.isNotEmpty) {
      rightWRS = _extractNumericValue(srtRight[0].wrs);
    }

    // Extract Tympanometry data with numeric value extraction
    if (table.left.isNotEmpty) {
      leftPP = _extractNumericValue(table.left[0].peakPressure);
    }
    if (table.right.isNotEmpty) {
      rightPP = _extractNumericValue(table.right[0].peakPressure);
    }

    return AudiometryDetailsModel(
      left250: leftF250,
      left500: leftF500,
      left1000: leftF1k,
      left1500: leftF1_5k,
      left2000: leftF2k,
      left3000: leftF3k,
      left4000: leftF4k,
      left6000: leftF6k,
      left8000: leftF8k,
      leftPP: leftPP,
      leftWRS: leftWRS,
      right250: rightF250,
      right500: rightF500,
      right1000: rightF1k,
      right1500: rightF1_5k,
      right2000: rightF2k,
      right3000: rightF3k,
      right4000: rightF4k,
      right6000: rightF6k,
      right8000: rightF8k,
      rightPP: rightPP,
      rightWRS: rightWRS,
      testDate: DateTime.now().toIso8601String(),
      created: DateTime.now().toIso8601String(),
      lastUpdated: DateTime.now().toIso8601String(),
    );
  }
} 