class MetaDataModel {
  final List<HealthAssessmentItem> data;
  final bool isDone;

  MetaDataModel({required this.data, required this.isDone});

  factory MetaDataModel.fromJson(Map<String, dynamic> json) {
    final dataList = (json['data'] as List)
        .map((item) => HealthAssessmentItem.fromJson(item))
        .toList();

    return MetaDataModel(
      data: dataList,
      isDone: json['isDone'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': data.map((item) => item.toJson()).toList(),
      'isDone': isDone,
    };
  }

  // Helper method to get all completed assessments
  List<HealthAssessmentItem> getCompletedAssessments() {
    return data.where((item) => item.getAssessmentStatus().completed).toList();
  }

  // Helper method to get all visible assessments
  List<HealthAssessmentItem> getVisibleAssessments() {
    return data.where((item) => item.getAssessmentStatus().show).toList();
  }
}

class HealthAssessmentItem {
  final Map<String, AssessmentStatus> assessments;

  HealthAssessmentItem({required this.assessments});

  factory HealthAssessmentItem.fromJson(Map<String, dynamic> json) {
    final assessments = <String, AssessmentStatus>{};

    json.forEach((key, value) {
      if (value is Map<String, dynamic>) {
        assessments[key] = AssessmentStatus.fromJson(value);
      }
    });

    return HealthAssessmentItem(assessments: assessments);
  }

  Map<String, dynamic> toJson() {
    final result = <String, dynamic>{};
    assessments.forEach((key, value) {
      result[key] = value.toJson();
    });
    return result;
  }

  // Helper method to get the assessment type name
  String getAssessmentType() {
    return assessments.keys.first;
  }

  // Helper method to get the assessment status
  AssessmentStatus getAssessmentStatus() {
    return assessments.values.first;
  }
}

class AssessmentStatus {
  final bool completed;
  final bool show;

  AssessmentStatus({required this.completed, required this.show});

  factory AssessmentStatus.fromJson(Map<String, dynamic> json) {
    return AssessmentStatus(
      completed: json['completed'] ?? false,
      show: json['show'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'completed': completed,
      'show': show,
    };
  }
}
