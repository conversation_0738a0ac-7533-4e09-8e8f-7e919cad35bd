class QuestionnaireData {
  final String label;
  final Map<String, QuestionnaireSet> questionnaires;

  QuestionnaireData({
    required this.label,
    required this.questionnaires,
  });

  factory QuestionnaireData.fromJson(Map<String, dynamic> json) {
    final questionnaires = <String, QuestionnaireSet>{};
    
    if (json['questionnaires'] != null) {
      final questionnairesJson = json['questionnaires'] as Map<String, dynamic>;
      questionnairesJson.forEach((key, value) {
        if (key != 'label' && value is Map<String, dynamic>) {
          questionnaires[key] = QuestionnaireSet.fromJson(value);
        }
      });
    }

    return QuestionnaireData(
      label: json['label'] ?? '',
      questionnaires: questionnaires,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'label': label,
      'questionnaires': questionnaires.map((key, value) => MapEntry(key, value.toJson())),
    };
  }
}

class QuestionnaireSet {
  final String label;
  final String description;
  final List<QuestionnaireSection>? data;
  final List<QuestionnaireOption>? options;
  final QuestionnaireExample? example;
  final String? metaQuest;
  final List<MetaAnswer>? metaAnswer;
  final List<QuestionnaireCategory>? categories;

  QuestionnaireSet({
    required this.label,
    required this.description,
    this.data,
    this.options,
    this.example,
    this.metaQuest,
    this.metaAnswer,
    this.categories,
  });

  factory QuestionnaireSet.fromJson(Map<String, dynamic> json) {
    return QuestionnaireSet(
      label: json['label'] ?? '',
      description: json['description'] ?? '',
      data: json['data'] != null
          ? (json['data'] as List).map((item) => QuestionnaireSection.fromJson(item)).toList()
          : null,
      options: json['options'] != null
          ? (json['options'] as List).map((item) => QuestionnaireOption.fromJson(item)).toList()
          : null,
      example: json['example'] != null ? QuestionnaireExample.fromJson(json['example']) : null,
      metaQuest: json['metaQuest'],
      metaAnswer: json['metaAnswer'] != null
          ? (json['metaAnswer'] as List).map((item) => MetaAnswer.fromJson(item)).toList()
          : null,
      categories: json['categories'] != null
          ? (json['categories'] as List).map((item) => QuestionnaireCategory.fromJson(item)).toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'label': label,
      'description': description,
      if (data != null) 'data': data!.map((item) => item.toJson()).toList(),
      if (options != null) 'options': options!.map((item) => item.toJson()).toList(),
      if (example != null) 'example': example!.toJson(),
      if (metaQuest != null) 'metaQuest': metaQuest,
      if (metaAnswer != null) 'metaAnswer': metaAnswer!.map((item) => item.toJson()).toList(),
      if (categories != null) 'categories': categories!.map((item) => item.toJson()).toList(),
    };
  }
}

class QuestionnaireSection {
  final String label;
  final String value;
  final List<QuestionnaireQuestion>? questions;
  final String? title;
  final int? minScale;
  final int? maxScale;
  final int? stepScale;
  final String? startLabel;
  final String? endLabel;
  final Map<String, String>? scale;
  final List<QuestionnaireOption>? options;

  QuestionnaireSection({
    required this.label,
    required this.value,
    this.questions,
    this.title,
    this.minScale,
    this.maxScale,
    this.stepScale,
    this.startLabel,
    this.endLabel,
    this.scale,
    this.options,
  });

  factory QuestionnaireSection.fromJson(Map<String, dynamic> json) {
    return QuestionnaireSection(
      label: json['label'] ?? '',
      value: json['value'] ?? '',
      questions: json['questions'] != null
          ? (json['questions'] as List).map((item) => QuestionnaireQuestion.fromJson(item)).toList()
          : null,
      title: json['title'],
      minScale: json['minScale'],
      maxScale: json['maxScale'],
      stepScale: json['stepScale'],
      startLabel: json['startLabel'],
      endLabel: json['endLabel'],
      scale: json['scale'] != null ? Map<String, String>.from(json['scale']) : null,
      options: json['options'] != null
          ? (json['options'] as List).map((item) => QuestionnaireOption.fromJson(item)).toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'label': label,
      'value': value,
      if (questions != null) 'questions': questions!.map((item) => item.toJson()).toList(),
      if (title != null) 'title': title,
      if (minScale != null) 'minScale': minScale,
      if (maxScale != null) 'maxScale': maxScale,
      if (stepScale != null) 'stepScale': stepScale,
      if (startLabel != null) 'startLabel': startLabel,
      if (endLabel != null) 'endLabel': endLabel,
      if (scale != null) 'scale': scale,
      if (options != null) 'options': options!.map((item) => item.toJson()).toList(),
    };
  }
}

class QuestionnaireQuestion {
  final String title;
  final String value;
  final int? minScale;
  final int? maxScale;
  final int? stepScale;
  final String? startLabel;
  final String? endLabel;
  final Map<String, String>? scale;

  QuestionnaireQuestion({
    required this.title,
    required this.value,
    this.minScale,
    this.maxScale,
    this.stepScale,
    this.startLabel,
    this.endLabel,
    this.scale,
  });

  factory QuestionnaireQuestion.fromJson(Map<String, dynamic> json) {
    return QuestionnaireQuestion(
      title: json['title'] ?? '',
      value: json['value'] ?? '',
      minScale: json['minScale'],
      maxScale: json['maxScale'],
      stepScale: json['stepScale'],
      startLabel: json['startLabel'],
      endLabel: json['endLabel'],
      scale: json['scale'] != null ? Map<String, String>.from(json['scale']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'value': value,
      if (minScale != null) 'minScale': minScale,
      if (maxScale != null) 'maxScale': maxScale,
      if (stepScale != null) 'stepScale': stepScale,
      if (startLabel != null) 'startLabel': startLabel,
      if (endLabel != null) 'endLabel': endLabel,
      if (scale != null) 'scale': scale,
    };
  }
}

class QuestionnaireOption {
  final String label;
  final String value;

  QuestionnaireOption({
    required this.label,
    required this.value,
  });

  factory QuestionnaireOption.fromJson(Map<String, dynamic> json) {
    return QuestionnaireOption(
      label: json['label'] ?? '',
      value: json['value'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'label': label,
      'value': value,
    };
  }
}

class QuestionnaireExample {
  final String description;
  final List<ExampleItem> examples;

  QuestionnaireExample({
    required this.description,
    required this.examples,
  });

  factory QuestionnaireExample.fromJson(Map<String, dynamic> json) {
    return QuestionnaireExample(
      description: json['description'] ?? '',
      examples: json['examples'] != null
          ? (json['examples'] as List).map((item) => ExampleItem.fromJson(item)).toList()
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'description': description,
      'examples': examples.map((item) => item.toJson()).toList(),
    };
  }
}

class ExampleItem {
  final String id;
  final String question;
  final bool withHearingAids;
  final bool withoutHearingAids;
  final Map<String, String> options;

  ExampleItem({
    required this.id,
    required this.question,
    required this.withHearingAids,
    required this.withoutHearingAids,
    required this.options,
  });

  factory ExampleItem.fromJson(Map<String, dynamic> json) {
    return ExampleItem(
      id: json['id'] ?? '',
      question: json['question'] ?? '',
      withHearingAids: json['withHearingAids'] ?? false,
      withoutHearingAids: json['withoutHearingAids'] ?? false,
      options: Map<String, String>.from(json['options'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'question': question,
      'withHearingAids': withHearingAids,
      'withoutHearingAids': withoutHearingAids,
      'options': options,
    };
  }
}

class MetaAnswer {
  final String label;
  final dynamic value;
  final List<MetaList>? metalist;

  MetaAnswer({
    required this.label,
    required this.value,
    this.metalist,
  });

  factory MetaAnswer.fromJson(Map<String, dynamic> json) {
    return MetaAnswer(
      label: json['label'] ?? '',
      value: json['value'],
      metalist: json['metalist'] != null
          ? (json['metalist'] as List).map((item) => MetaList.fromJson(item)).toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'label': label,
      'value': value,
      if (metalist != null) 'metalist': metalist!.map((item) => item.toJson()).toList(),
    };
  }
}

class MetaList {
  final String label;
  final String value;
  final MetaOptions options;

  MetaList({
    required this.label,
    required this.value,
    required this.options,
  });

  factory MetaList.fromJson(Map<String, dynamic> json) {
    return MetaList(
      label: json['Label'] ?? json['label'] ?? '',
      value: json['value'] ?? '',
      options: MetaOptions.fromJson(json['options'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'Label': label,
      'value': value,
      'options': options.toJson(),
    };
  }
}

class MetaOptions {
  final String type;
  final List<SubMetaQuest> subMetaQuest;

  MetaOptions({
    required this.type,
    required this.subMetaQuest,
  });

  factory MetaOptions.fromJson(Map<String, dynamic> json) {
    return MetaOptions(
      type: json['type'] ?? '',
      subMetaQuest: json['subMetaQuest'] != null
          ? (json['subMetaQuest'] as List).map((item) => SubMetaQuest.fromJson(item)).toList()
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'subMetaQuest': subMetaQuest.map((item) => item.toJson()).toList(),
    };
  }
}

class SubMetaQuest {
  final String label;
  final String value;

  SubMetaQuest({
    required this.label,
    required this.value,
  });

  factory SubMetaQuest.fromJson(Map<String, dynamic> json) {
    return SubMetaQuest(
      label: json['label'] ?? '',
      value: json['value'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'label': label,
      'value': value,
    };
  }
}

class QuestionnaireCategory {
  final String label;
  final String value;

  QuestionnaireCategory({
    required this.label,
    required this.value,
  });

  factory QuestionnaireCategory.fromJson(Map<String, dynamic> json) {
    return QuestionnaireCategory(
      label: json['label'] ?? '',
      value: json['value'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'label': label,
      'value': value,
    };
  }
}

// Model for storing questionnaire responses
class QuestionnaireResponse {
  final String questionnaireType;
  final Map<String, dynamic> answers;
  final DateTime completedAt;

  QuestionnaireResponse({
    required this.questionnaireType,
    required this.answers,
    required this.completedAt,
  });

  factory QuestionnaireResponse.fromJson(Map<String, dynamic> json) {
    return QuestionnaireResponse(
      questionnaireType: json['questionnaireType'] ?? '',
      answers: Map<String, dynamic>.from(json['answers'] ?? {}),
      completedAt: DateTime.parse(json['completedAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'questionnaireType': questionnaireType,
      'answers': answers,
      'completedAt': completedAt.toIso8601String(),
    };
  }
}
