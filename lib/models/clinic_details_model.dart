class ClinicDetailsModel {
  String? clinicName;
  List<String>? kitName;
  bool? paymentFlag;
  String? programName;
  bool? showProvider;

  ClinicDetailsModel({
    this.clinicName,
    this.kitName,
    this.paymentFlag,
    this.programName,
    this.showProvider,
  });

  ClinicDetailsModel.fromJson(Map<String, dynamic> json) {
    clinicName = json['clinicName'];
    kitName = json['kitName'].cast<String>();
    paymentFlag = json['paymentFlag'];
    programName = json['programName'];
    showProvider = json['showProvider'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['clinicName'] = clinicName;
    data['kitName'] = kitName;
    data['paymentFlag'] = paymentFlag;
    data['programName'] = programName;
    data['showProvider'] = showProvider;
    return data;
  }
}
