import 'package:get/get.dart';
import 'package:gt_plus/ble_reader_page.dart';
import 'package:gt_plus/modules/Qr/binding/qr_binding.dart';
import 'package:gt_plus/modules/Qr/view/qr_view.dart';
import 'package:gt_plus/modules/audiometryReportDataEntry/binding/audiometry_report_data_entry_binding.dart';
import 'package:gt_plus/modules/audiometryReportDataEntry/view/audiometry_report_data_entry_view.dart';
import 'package:gt_plus/modules/basicDetails/binding/basic_details_binding.dart';
import 'package:gt_plus/modules/basicDetails/view/basic_detail_view.dart';
import 'package:gt_plus/modules/bloodPressure/binding/blood_pressure_binding.dart';
import 'package:gt_plus/modules/bloodPressure/view/blood_pressure_reading_view.dart';
import 'package:gt_plus/modules/cognivueReportDataEntry/binding/cognivue_report_data_entry_binding.dart';
import 'package:gt_plus/modules/cognivueReportDataEntry/view/cognivue_report_data_entry_view.dart';
import 'package:gt_plus/modules/extraPatientData/binding/extra_patient_data_binding.dart';
import 'package:gt_plus/modules/extraPatientData/view/extra_patient_data_view.dart';
import 'package:gt_plus/modules/speechInNoiseReportDataEntry/binding/speech_in_noise_report_data_entry_binding.dart';
import 'package:gt_plus/modules/speechInNoiseReportDataEntry/view/speech_in_noise_report_data_entry_view.dart';
import 'package:gt_plus/modules/meta/binding/meta_binding.dart';
import 'package:gt_plus/modules/meta/view/meta_view.dart';
import 'package:gt_plus/modules/microscope/binding/microscope_binding.dart';
import 'package:gt_plus/modules/microscope/view/after_microscope_screen.dart';
import 'package:gt_plus/modules/microscope/view/microscope_preview_view.dart';
import 'package:gt_plus/modules/microscope/view/microscope_view.dart';
import 'package:gt_plus/modules/microscope/view/wifi_instruction_view.dart';
import 'package:gt_plus/modules/ppg/binding/ppg_binding.dart';
import 'package:gt_plus/modules/ppg/view/ppg_reading_view.dart';
import 'package:gt_plus/modules/scanImages/binding/scan_images_binding.dart';
import 'package:gt_plus/modules/scanImages/view/face_mesh_view.dart';
import 'package:gt_plus/modules/scanImages/view/scan_image_capture_screen.dart';
import 'package:gt_plus/modules/scanImages/view/scan_image_preview_screen.dart';
import 'package:gt_plus/modules/scanImages/view/scan_images_main_screen.dart';
import 'package:gt_plus/modules/setting/binding/setting_binding.dart';
import 'package:gt_plus/modules/setting/view/setting_view.dart';
import 'package:gt_plus/modules/splash/binding/splash_binding.dart';
import 'package:gt_plus/modules/splash/view/splash_view.dart';
import 'package:gt_plus/modules/temprature/binding/temperature_binding.dart';
import 'package:gt_plus/modules/temprature/view/temperature_reading_view.dart';
import 'package:gt_plus/modules/questionnaire/binding/questionnaire_binding.dart';
import 'package:gt_plus/modules/questionnaire/view/rhhi_questionnaire_view.dart';
import 'package:gt_plus/modules/questionnaire/view/aphab_questionnaire_view.dart';
import 'package:gt_plus/modules/questionnaire/view/tfi_questionnaire_view.dart';
import 'package:gt_plus/modules/questionnaire/view/cosi_questionnaire_view.dart';

import 'modules/login/binding/login_binding.dart';
import 'modules/login/view/login_dob_view.dart';
import 'modules/login/view/login_phone_view.dart';



class AppRoutes {
  static List<GetPage> getPages = [
    GetPage(
      name: SplashView.routeName,
      page: () => const SplashView(),
      bindings: SplashBinding.binding,
    ),
    GetPage(
      name: PpgReadingView.routeName,
      page: () => const PpgReadingView(),
      bindings: PpgBinding.binding,
    ),
    GetPage(
      name: BasicDetailView.routeName,
      page: () => const BasicDetailView(),
      bindings: BasicDetailsBinding.binding,
    ),
    GetPage(
      name: ScanImagesMainView.routeName,
      page: () => const ScanImagesMainView(),
      bindings: ScanImagesBinding.binding,
    ),
    GetPage(
      name: TemperatureReadingView.routeName,
      page: () => const TemperatureReadingView(),
      bindings: TemperatureBinding.binding,
    ),
    GetPage(
      name: BloodPressureReadingView.routeName,
      page: () => const BloodPressureReadingView(),
      bindings: BloodPressureBinding.binding,
    ),
    GetPage(
      name: FaceMeshView.routeName,
      page: () => const FaceMeshView(),
      bindings: FaceMeshBinding.binding,
    ),
    GetPage(
      name: ScanImageCaptureScreen.routeName,
      page: () => const ScanImageCaptureScreen(),
      bindings: ScanImagesBinding.binding,
    ),
    GetPage(
      name: ScanImagePreviewScreen.routeName,
      page: () => const ScanImagePreviewScreen(),
      bindings: ScanImagesBinding.binding,
    ),
    GetPage(
      name: AudiometryReportDataEntryView.routeName,
      page: () => const AudiometryReportDataEntryView(),
      bindings: AudiometryReportDataEntryBinding.binding,
    ),
    GetPage(
      name: CognivueReportDataEntryView.routeName,
      page: () => const CognivueReportDataEntryView(),
      bindings: CognivueReportDataEntryBinding.binding,
    ),
    GetPage(
      name: SpeechInNoiseReportDataEntryView.routeName,
      page: () => const SpeechInNoiseReportDataEntryView(),
      bindings: SpeechInNoiseReportDataEntryBinding.binding,
    ),
    GetPage(
      name: ExtraPatientDataView.routeName,
      page: () => const ExtraPatientDataView(),
      bindings: ExtraPatientDataBinding.binding,
    ),
    GetPage(
      name: QrView.routeName,
      page: () => const QrView(),
      bindings: QrBinding.binding,
    ),
    GetPage(
      name: LoginPhoneView.routeName,
      page: () => const LoginPhoneView(),
      bindings: LoginBinding.binding,
    ),
    GetPage(
      name: LoginDobView.routeName,
      page: () => const LoginDobView(),
      bindings: LoginBinding.binding,
    ),
    GetPage(
      name: MetaView.routeName,
      page: () => const MetaView(),
      bindings: MetaBinding.binding,
    ),
    GetPage(
      name: SettingView.routeName,
      page: () => const SettingView(),
      bindings: SettingBinding.binding,
    ),
    GetPage(
      name: BleReaderPage.routeName,
      page: () => const BleReaderPage(),
    ),
    GetPage(
      name: MicroscopeView.routeName,
      page: () => const MicroscopeView(),
      bindings: MicroscopeBinding.binding,
    ),
    GetPage(
      name: WifiInstructionView.routeName,
      page: () => const WifiInstructionView(),
    ),
    GetPage(
      name: AfterMicroscopeScreen.routeName,
      page: () => const AfterMicroscopeScreen(),
      bindings: MicroscopeBinding.binding,
    ),
    GetPage(
      name: MicroscopePreviewView.routeName,
      page: () => const MicroscopePreviewView(),
      bindings: MicroscopeBinding.binding,
    ),
    GetPage(
      name: RHHIQuestionnaireView.routeName,
      page: () => const RHHIQuestionnaireView(),
      bindings: RHHIQuestionnaireBinding.binding,
    ),
    GetPage(
      name: APHABQuestionnaireView.routeName,
      page: () => const APHABQuestionnaireView(),
      bindings: APHABQuestionnaireBinding.binding,
    ),
    GetPage(
      name: TFIQuestionnaireView.routeName,
      page: () => const TFIQuestionnaireView(),
      bindings: TFIQuestionnaireBinding.binding,
    ),
    GetPage(
      name: COSIQuestionnaireView.routeName,
      page: () => const COSIQuestionnaireView(),
      bindings: COSIQuestionnaireBinding.binding,
    ),
  ];
}
