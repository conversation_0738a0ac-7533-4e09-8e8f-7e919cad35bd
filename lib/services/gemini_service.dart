import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter_gemini/flutter_gemini.dart';
import 'package:get/get.dart';
import '../models/audiometry_details_model.dart';
import '../models/cognivue_details_model.dart';
import '../models/gemini_audiogram_model.dart';
import '../models/medical_report_model.dart';
import '../utils/logger.dart';
import '../modules/gemini_image_to_text/gemini_report_card.dart';
import '../services/remoteConfig/firebase_remote_config_service.dart';

/// Unified Gemini service to handle all Gemini AI operations including text and image processing
class GeminiService extends GetxService {
  static GeminiService get instance => Get.find<GeminiService>();

  late final Gemini _gemini;
  final String _apiKey = 'AIzaSyCShujEhsjkWaEWbSO1pmTGhe1eJg66Bzc';
  bool _isInitialized = false;
  late final AppLogger _logger;
  final FirebaseRemoteConfigService _remoteConfigService = FirebaseRemoteConfigService();

  GeminiService() {
    _logger = AppLogger('GEMINI');
  }

  // Define report configurations
  late final Map<ReportType, ReportConfig> _reportConfigs;

  /// Initialize the Gemini service
  Future<GeminiService> init() async {
    if (!_isInitialized) {
      Gemini.init(apiKey: _apiKey);
      _gemini = Gemini.instance;
      _isInitialized = true;
      _logger.info('Initialized Gemini API');
      
      // Initialize report configs with prompts from remote config
      _reportConfigs = {
        ReportType.audiometry: ReportConfig(
          prompt: _remoteConfigService.getAudiometryPrompt(),
          fromJson: (json) => AudiometryDetailsModel.fromJson(json),
          debugPrefix: 'AUDIOMETRY',
        ),
        ReportType.cognivue: ReportConfig(
          prompt: _remoteConfigService.getCognivuePrompt(),
          fromJson: (json) => CognivueDetailsModel.fromJson(json),
          debugPrefix: 'COGNIVUE',
        ),
      };
      _logger.info('Initialized report configs with remote config prompts');
    }
    return this;
  }

  /// Process image from file and extract report data based on report type
  Future<MedicalReportModel?> processReportImageFile(
      File imageFile, ReportType reportType) async {
    final config = _reportConfigs[reportType];
    if (config == null) {
      _logger.error('Unknown report type: $reportType');
      return null;
    }
    
    final reportLogger = _logger.createSubLogger(config.debugPrefix);
    
    try {
      reportLogger.info('Starting to process image file: ${imageFile.path}');
      final Uint8List imageBytes = await imageFile.readAsBytes();
      reportLogger.info('Successfully read image bytes, size: ${imageBytes.length}');
      return processReportImageBytes(imageBytes, reportType);
    } catch (e) {
      reportLogger.error('Error processing image file: $e');
      return null;
    }
  }

  /// Process image as bytes and extract report data based on report type
  Future<MedicalReportModel?> processReportImageBytes(
      Uint8List imageBytes, ReportType reportType) async {
    final config = _reportConfigs[reportType];
    if (config == null) {
      _logger.error('Unknown report type: $reportType');
      return null;
    }
    
    final reportLogger = _logger.createSubLogger(config.debugPrefix);
    
    try {
      reportLogger.info('Starting to process image bytes');
      
      final result = await _geminiTextAndImage(config.prompt, imageBytes);
      
      if (result == null) {
        reportLogger.error('Null response from Gemini API');
        return null;
      }
      
      reportLogger.info('Gemini API response received: ${result.substring(0, result.length > 100 ? 100 : result.length)}...');
      
      final jsonText = _extractJsonFromText(result);
      
      if (jsonText == null) {
        reportLogger.error('Could not extract JSON from response');
        return null;
      }
      
      reportLogger.info('Extracted JSON: ${jsonText.substring(0, jsonText.length > 100 ? 100 : jsonText.length)}...');

      try {
        final Map<String, dynamic> jsonMap = jsonDecode(jsonText);
        reportLogger.info('Successfully parsed JSON, keys: ${jsonMap.keys.join(', ')}');
        return config.fromJson(jsonMap);
      } catch (e) {
        reportLogger.error('Error parsing JSON: $e');
        reportLogger.error('Response text: $result');
        return null;
      }
    } catch (e) {
      reportLogger.error('Error in API processing: $e');
      return null;
    }
  }
  
  // Legacy methods for backward compatibility
  Future<AudiometryDetailsModel?> processAudiometryImageFile(File imageFile) async {
    final result = await processReportImageFile(imageFile, ReportType.audiometry);
    return result as AudiometryDetailsModel?;
  }

  Future<CognivueDetailsModel?> processCognivueImageFile(File imageFile) async {
    final result = await processReportImageFile(imageFile, ReportType.cognivue);
    return result as CognivueDetailsModel?;
  }

  Future<AudiometryDetailsModel?> processAudiometryImageBytes(Uint8List imageBytes) async {
    final result = await processReportImageBytes(imageBytes, ReportType.audiometry);
    return result as AudiometryDetailsModel?;
  }

  Future<CognivueDetailsModel?> processCognivueImageBytes(Uint8List imageBytes) async {
    final result = await processReportImageBytes(imageBytes, ReportType.cognivue);
    return result as CognivueDetailsModel?;
  }

  /// Process audiogram image and return GeminiAudiogramModel
  /// This provides more detailed audiogram data
  Future<GeminiAudiogramModel?> processDetailedAudiogramImage(
      Uint8List imageBytes) async {
    try {
      // Get response from Gemini using the remote config prompt
      final outputText =
          await _geminiTextAndImage(_remoteConfigService.getAudiometryPrompt(), imageBytes);
      if (outputText == null) return null;

      try {
        // Parse the JSON response
        final Map<String, dynamic> jsonMap = jsonDecode(outputText);
        return GeminiAudiogramModel.fromJson(jsonMap);
      } catch (e) {
        debugPrint('Error parsing JSON: $e');

        // Try to extract JSON from text that might contain additional content
        final jsonText = _extractJsonFromText(outputText);
        if (jsonText != null) {
          try {
            final Map<String, dynamic> jsonMap = jsonDecode(jsonText);
            return GeminiAudiogramModel.fromJson(jsonMap);
          } catch (e) {
            debugPrint('Error parsing extracted JSON: $e');
          }
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error processing image: $e');
      return null;
    }
  }

  /// Helper method to call Gemini's API and handle common error cases
  Future<String?> _geminiTextAndImage(
      String promptText, Uint8List imageBytes) async {
    try {
      _logger.info('Calling Gemini API with prompt and image');
      
      final result = await _gemini.prompt(
        parts: [
          Part.text(promptText),
          Part.bytes(imageBytes),
        ],
      );

      if (result == null) {
        _logger.error('Null response from Gemini API');
        return null;
      }

      final String? outputText = result.output;
      if (outputText == null || outputText.isEmpty) {
        _logger.error('Empty response from Gemini API');
        return null;
      }

      _logger.info('Received non-empty response from Gemini API');
      return outputText;
    } catch (e) {
      _logger.error('Error in Gemini API call: $e');
      return null;
    }
  }

  /// Extract a JSON string from text that might contain other content
  String? _extractJsonFromText(String text) {
    final jsonStart = text.indexOf('{');
    final jsonEnd = text.lastIndexOf('}');

    if (jsonStart >= 0 && jsonEnd > jsonStart) {
      return text.substring(jsonStart, jsonEnd + 1);
    }

    return null;
  }
}

/// Configuration for a report type
class ReportConfig {
  final String prompt;
  final Function(Map<String, dynamic>) fromJson;
  final String debugPrefix;

  ReportConfig({
    required this.prompt,
    required this.fromJson,
    required this.debugPrefix,
  });
}
