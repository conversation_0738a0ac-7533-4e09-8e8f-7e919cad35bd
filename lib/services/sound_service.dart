import 'dart:io';
import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

class SoundService {
  static final SoundService _instance = SoundService._internal();
  factory SoundService() => _instance;
  SoundService._internal();

  static const MethodChannel _channel = MethodChannel('com.telehealth.sound/channel');
  late AudioPlayer _audioPlayer;
  bool _isInitialized = false;

  /// Initialize the sound service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      _audioPlayer = AudioPlayer();
      _isInitialized = true;
      debugPrint('SoundService initialized successfully');
    } catch (e) {
      debugPrint('Failed to initialize SoundService: $e');
    }
  }

  /// Play camera shutter sound
  Future<void> playShutterSound() async {
    try {
      if (Platform.isIOS) {
        // Use native iOS system sound for better performance and consistency
        await _playNativeShutterSound();
      } else if (Platform.isAndroid) {
        // Use native Android system sound
        await _playNativeShutterSound();
      } else {
        // Fallback for other platforms using asset sound
        await _playAssetShutterSound();
      }
    } catch (e) {
      debugPrint('Failed to play shutter sound: $e');
      // Try fallback method
      await _playAssetShutterSound();
    }
  }

  /// Play native system shutter sound (iOS/Android)
  Future<void> _playNativeShutterSound() async {
    try {
      await _channel.invokeMethod('playShutterSound');
    } catch (e) {
      debugPrint('Failed to play native shutter sound: $e');
      rethrow;
    }
  }

  /// Play shutter sound from assets (fallback)
  Future<void> _playAssetShutterSound() async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      // For now, just use haptic feedback as fallback
      // You can add a custom shutter sound asset later if needed
      await HapticFeedback.lightImpact();
      debugPrint('Played haptic feedback as shutter sound fallback');
    } catch (e) {
      debugPrint('Failed to play asset shutter sound: $e');
      // If all else fails, use system feedback
      await HapticFeedback.lightImpact();
    }
  }

  /// Dispose resources
  Future<void> dispose() async {
    if (_isInitialized) {
      await _audioPlayer.dispose();
      _isInitialized = false;
    }
  }
}
