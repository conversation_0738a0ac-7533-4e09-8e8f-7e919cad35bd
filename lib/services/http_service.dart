import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gt_plus/modules/BleFlutter/ble_flutter_controller.dart';
import 'package:gt_plus/models/refresh_token_model.dart';
import 'package:gt_plus/services/prefs_service.dart';
import 'package:http/http.dart' as http;
import 'package:gt_plus/utils/appConst/app_urls.dart';
import '../utils/reusableWidgets/reusable_dialog.dart';

import '../modules/login/view/login_phone_view.dart';

class HttpService {
  static const int _timeoutInSeconds = 20;
  static const String _contentType = "application/json";

  static Map<String, String> get defaultHeaders => {
        "Content-Type": _contentType,
      };

  // Build query parameters
  String _buildQueryParams(Map<String, dynamic>? params) {
    if (params == null || params.isEmpty) return "";

    return params.entries
        .map((entry) => "${entry.key}=${entry.value}")
        .join("&");
  }

  // Log request details
  void _logRequest(String method, String url,
      {Map<String, dynamic>? body, Map<String, dynamic>? header}) {
    debugPrint("===== $method Request =====");
    debugPrint("URL: $url");
    debugPrint("Body: ${body.toString()}");
    debugPrint("Header: $header");
  }

  // Log response details
  void _logResponse(http.Response response) {
    debugPrint("===== Response =====");
    debugPrint("Status: ${response.statusCode}");
    debugPrint("Body: ${response.body}");
  }

  // Format URL with query parameters
  String _formatUrl(ApiUrls apiUrl, Map<String, dynamic>? params) {
    final queryParams = _buildQueryParams(params);
    return apiUrl.getUrl() + (queryParams.isNotEmpty ? "?$queryParams" : "");
  }

  // Show error dialog for 460 errors
  static void _showErrorDialog(String message) {
    ReusableDialog.show(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 8),
          const Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 48,
          ),
          const SizedBox(height: 16),
          const Text(
            'Error',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            message,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              ReusableDialog.close();
            },
            child: const Text('OK'),
          ),
          const SizedBox(height: 8),
        ],
      ),
    );
  }

  // Handle API response
  static T? handleResponse<T>(
    http.Response response,
    T Function(Map<String, dynamic> data) onSuccess,
  ) {
    switch (response.statusCode) {
      case 200:
        Map<String, dynamic> responseData = json.decode(response.body);
        return onSuccess(responseData);
      case 400:
        return onSuccess(json.decode(response.body));
      case 401:
        // Return null to indicate 401 - the calling code should handle refresh
        return null;
      case 460:
        Map<String, dynamic> responseData = json.decode(response.body);
        String errorMessage = responseData['message'] ?? 'An error occurred';
        _showErrorDialog(errorMessage);
        return null;
      default:
        debugPrint("Unhandled status code: ${response.statusCode}");
        return null;
    }
  }

  // Handle API response with automatic token refresh for 401 errors
  static Future<T?> handleResponseWithRefresh<T>(
    http.Response response,
    T Function(Map<String, dynamic> data) onSuccess,
    Future<http.Response> Function() retryRequest,
  ) async {
    switch (response.statusCode) {
      case 200:
        Map<String, dynamic> responseData = json.decode(response.body);
        return onSuccess(responseData);
      case 400:
        return onSuccess(json.decode(response.body));
      case 401:
        debugPrint("401 Unauthorized - Attempting token refresh");
        bool refreshSuccess = await _handleRefreshToken();
        if (refreshSuccess) {
          debugPrint("Token refreshed successfully, retrying request");
          try {
            http.Response retryResponse = await retryRequest();
            debugPrint("Retry request after token refresh. Status: \\${retryResponse.statusCode}");
            return handleResponse<T>(retryResponse, onSuccess);
          } catch (e) {
            debugPrint("Error during retry request: $e");
            handleLogout();
            return null;
          }
        } else {
          debugPrint("Token refresh failed, logging out user");
          handleLogout();
          return null;
        }
      case 460:
        Map<String, dynamic> responseData = json.decode(response.body);
        String errorMessage = responseData['message'] ?? 'An error occurred';
        _showErrorDialog(errorMessage);
        return null;
      default:
        debugPrint("Unhandled status code: \\${response.statusCode}");
        return null;
    }
  }

  // Handle refresh token logic
  static Future<bool> _handleRefreshToken() async {
    try {
      final prefsService = PrefsService();
      String refreshToken = await prefsService.getRefreshToken();
      debugPrint("[RefreshToken] Retrieved refresh token: \\${refreshToken.isNotEmpty ? refreshToken.substring(0, 6) + '...' : 'EMPTY'}");

      if (refreshToken.isEmpty) {
        debugPrint("No refresh token available");
        return false;
      }

      final headers = {
        ...defaultHeaders,
        "Authorization": "Bearer $refreshToken",
      };
      debugPrint("[RefreshToken] Sending refresh request to: \\${ApiUrls.getRefreshToken.getUrl()}");
      debugPrint("[RefreshToken] Headers: \\${headers.map((k, v) => MapEntry(k, k == 'Authorization' ? v.substring(0, 15) + '...' : v))}");

      final response = await http
          .get(
            Uri.parse(ApiUrls.getRefreshToken.getUrl()),
            headers: headers,
          )
          .timeout(const Duration(seconds: _timeoutInSeconds));

      debugPrint("[RefreshToken] Response status: \\${response.statusCode}");
      debugPrint("[RefreshToken] Response body: \\${response.body}");

      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = json.decode(response.body);
        RefreshTokenModel refreshTokenModel = RefreshTokenModel.fromJson(responseData);
        debugPrint("[RefreshToken] Parsed access token: \\${refreshTokenModel.accessToken != null ? refreshTokenModel.accessToken!.substring(0, 6) + '...' : 'NULL'}");

        if (refreshTokenModel.accessToken != null) {
          await prefsService.setAccessToken(refreshTokenModel.accessToken!);
          debugPrint("Access token refreshed successfully");
          return true;
        } else {
          debugPrint("[RefreshToken] No access token found in response");
        }
      } else {
        debugPrint("Failed to refresh token: \\${response.statusCode}");
      }
      return false;
    } catch (e) {
      debugPrint("Error during token refresh: $e");
      return false;
    }
  }

  static Future<void> handleLogout() async {
    final bleFlutterController = Get.find<BleFlutterController>();
    final prefsService = PrefsService();
    await bleFlutterController.disconnectAndResetAll();
    await prefsService.clearAll();
    Get.offAllNamed(LoginPhoneView.routeName);
    debugPrint("User logged out...");
  }

  // GET request
  Future<http.Response> get(
    ApiUrls apiUrl, {
    Map<String, dynamic>? params,
    Map<String, String>? headers,
  }) async {
    final url = _formatUrl(apiUrl, params);
    _logRequest("GET", url, header: headers);

    final response = await http
        .get(
          Uri.parse(url),
          headers: headers ?? defaultHeaders,
        )
        .timeout(const Duration(seconds: _timeoutInSeconds));

    _logResponse(response);
    return response;
  }

  // POST request
  Future<http.Response> post(
    ApiUrls apiUrl, {
    Map<String, dynamic>? params,
    Map<String, dynamic>? body,
    Map<String, String>? headers,
  }) async {
    final url = _formatUrl(apiUrl, params);
    _logRequest("POST", url, body: body, header: headers);

    final requestHeaders = {...defaultHeaders, ...?headers};

    http.Response response;
    if (body != null) {
      response = await http
          .post(
            Uri.parse(url),
            headers: requestHeaders,
            body: json.encode(body),
          )
          .timeout(const Duration(seconds: _timeoutInSeconds));
    } else {
      response = await http
          .post(
            Uri.parse(url),
            headers: requestHeaders,
          )
          .timeout(const Duration(seconds: _timeoutInSeconds));
    }

    _logResponse(response);
    return response;
  }

  // PUT request
  Future<http.Response> put(
    ApiUrls apiUrl, {
    Map<String, dynamic>? params,
    Map<String, dynamic>? body,
    Map<String, String>? headers,
  }) async {
    final url = _formatUrl(apiUrl, params);
    _logRequest("PUT", url, body: body, header: headers);

    final requestHeaders = {...defaultHeaders, ...?headers};

    final response = await http
        .put(
          Uri.parse(url),
          headers: requestHeaders,
          body: body != null ? json.encode(body) : null,
        )
        .timeout(const Duration(seconds: _timeoutInSeconds));

    _logResponse(response);
    return response;
  }

  // DELETE request
  Future<http.Response> delete(
    ApiUrls apiUrl, {
    Map<String, dynamic>? params,
    Map<String, String>? headers,
  }) async {
    final url = _formatUrl(apiUrl, params);
    _logRequest("DELETE", url, header: headers);

    final response = await http
        .delete(
          Uri.parse(url),
          headers: headers ?? defaultHeaders,
        )
        .timeout(
          const Duration(seconds: _timeoutInSeconds),
        );

    _logResponse(response);
    return response;
  }
}
