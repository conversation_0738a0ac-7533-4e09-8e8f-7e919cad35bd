import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';

import '../models/clinic_details_model.dart';

class PrefsService {
  late SharedPreferences prefs;
  late Future<void> _prefsInitFuture;

  PrefsService() {
    _prefsInitFuture = _initPrefs();
  }

  Future<void> _initPrefs() async {
    prefs = await SharedPreferences.getInstance();
  }

  Future<String> getRefreshToken() async {
    await _prefsInitFuture;
    String? data = prefs.getString(PrefsKeys.refreshToken);
    return data ?? "";
  }

  Future<void> setRefreshToken(String refreshToken) async {
    await prefs.setString(PrefsKeys.refreshToken, refreshToken);
  }

  Future<String> getAccessToken() async {
    await _prefsInitFuture;
    String? data = prefs.getString(PrefsKeys.accessToken);
    return data ?? "";
  }

  Future<void> setAccessToken(String accessToken) async {
    await prefs.setString(PrefsKeys.accessToken, accessToken);
  }

  Future<ClinicDetailsModel?> getClinicDetails() async {
    await _prefsInitFuture;
    final String? modelString = prefs.getString('clinic_details');
    if (modelString == null) {
      return null;
    }
    final Map<String, dynamic> modelJson = jsonDecode(modelString);
    return ClinicDetailsModel.fromJson(modelJson);
  }

  // Setter for clinic_name
  Future<void> setClinicDetails(ClinicDetailsModel clinicModel) async {
    final String modelString = jsonEncode(clinicModel);
    await prefs.setString(PrefsKeys.clinicDetails, modelString);
  }

  Future<String> getIdentifier() async {
    await _prefsInitFuture;
    String? data = prefs.getString(PrefsKeys.identifier);
    return data ?? "";
  }

  Future<void> setIdentifier(String identifier) async {
    await prefs.setString(PrefsKeys.identifier, identifier);
  }

  // Future<Map<String, dynamic>> getPpgDevice() async {
  //   await _prefsInitFuture;
  //   String? data = prefs.getString(PrefsKeys.ppgDeviceMac);
  //   if (data != null) {
  //     try {
  //       return Map<String, dynamic>.from(jsonDecode(data));
  //     } catch (e) {
  //       print("Error decoding PPG device data: $e");
  //     }
  //   }
  //   return {};
  // }
  //
  // Future<void> setPpgDevice(Map<String, dynamic> ppgDeviceMac) async {
  //   await prefs.setString(PrefsKeys.ppgDeviceMac, jsonEncode(ppgDeviceMac));
  // }

  Future<void> setPpgRemoteId(String ppgRemoteId) async {
    await prefs.setString(PrefsKeys.ppgRemoteId, ppgRemoteId);
  }

  Future<String> getPpgRemoteId() async {
    await _prefsInitFuture;
    String? data = prefs.getString(PrefsKeys.ppgRemoteId);
    return data ?? "";
  }

  Future<void> setTempRemoteId(String tempRemoteId) async {
    await prefs.setString(PrefsKeys.tempRemoteId, tempRemoteId);
  }

  Future<String> getTempRemoteId() async {
    await _prefsInitFuture;
    String? data = prefs.getString(PrefsKeys.tempRemoteId);
    return data ?? "";
  }

  Future<void> setBpRemoteId(String bpRemoteId) async {
    await prefs.setString(PrefsKeys.bpRemoteId, bpRemoteId);
  }

  Future<String> getBpRemoteId() async {
    await _prefsInitFuture;
    String? data = prefs.getString(PrefsKeys.bpRemoteId);
    return data ?? "";
  }

  Future<void> setDemoMode(bool isDemoMode) async {
    await prefs.setBool(PrefsKeys.isDemoMode, isDemoMode);
  }

  Future<bool> getDemoMode() async {
    await _prefsInitFuture;
    bool? data = prefs.getBool(PrefsKeys.isDemoMode);
    return data ?? false;
  }

  Future<void> setQrValue({required String qrValue}) async {
    await prefs.setString(PrefsKeys.qrValue, qrValue);
  }

  Future<String> getQrValue() async {
    await _prefsInitFuture;
    String? data = prefs.getString(PrefsKeys.qrValue);
    return data ?? "";
  }

  Future<void> setDobFromLogin(String dob) async {
    await _prefsInitFuture;
    await prefs.setString(PrefsKeys.dobFromLogin, dob);
  }

  Future<String> getDobFromLogin() async {
    await _prefsInitFuture;
    String? dob = prefs.getString(PrefsKeys.dobFromLogin);
    return dob ?? "";
  }

  Future<void> setLoginMethod(String method) async {
    await _prefsInitFuture;
    await prefs.setString(PrefsKeys.loginMethod, method);
  }

  Future<String> getLoginMethod() async {
    await _prefsInitFuture;
    String? method = prefs.getString(PrefsKeys.loginMethod);
    return method ?? "";
  }

  Future<void> setPatientName(String patientName) async {
    await _prefsInitFuture;
    await prefs.setString(PrefsKeys.patientName, patientName);
  }

  Future<String> getPatientName() async {
    await _prefsInitFuture;
    String? patientName = prefs.getString(PrefsKeys.patientName);
    return patientName ?? "";
  }

  Future<void> setFirstNameFromLogin(String firstName) async {
    await _prefsInitFuture;
    await prefs.setString(PrefsKeys.firstNameFromLogin, firstName);
  }

  Future<String> getFirstNameFromLogin() async {
    await _prefsInitFuture;
    String? firstName = prefs.getString(PrefsKeys.firstNameFromLogin);
    return firstName ?? "";
  }

  Future<void> setLastNameFromLogin(String lastName) async {
    await _prefsInitFuture;
    await prefs.setString(PrefsKeys.lastNameFromLogin, lastName);
  }

  Future<String> getLastNameFromLogin() async {
    await _prefsInitFuture;
    String? lastName = prefs.getString(PrefsKeys.lastNameFromLogin);
    return lastName ?? "";
  }

  Future<void> clearByKey(String key) async {
    await _prefsInitFuture;
    await prefs.remove(key);
  }

  Future<void> clearAll() async {
    await _prefsInitFuture;
    await prefs.clear();
  }

  // Method to specifically clear login-related information
  Future<void> clearLoginInfo() async {
    await clearByKey(PrefsKeys.dobFromLogin);
    await clearByKey(PrefsKeys.loginMethod);
    await clearByKey(PrefsKeys.patientName);
    await clearByKey(PrefsKeys.firstNameFromLogin);
    await clearByKey(PrefsKeys.lastNameFromLogin);
  }
}

class PrefsKeys {
  static const String refreshToken = "refresh_token";
  static const String accessToken = "access_token";
  static const String qrValue = "qr_value";
  static const String clinicDetails = "clinic_details";
  static const String identifier = "identifier";
  static const String ppgRemoteId = "ppgRemoteId";
  static const String tempRemoteId = "tempRemoteId";
  static const String bpRemoteId = "bpRemoteId";
  static const String isDemoMode = "isDemoMode";
  static const String dobFromLogin = "dob_from_login";
  static const String loginMethod = "login_method";
  static const String patientName = "patient_name";
  static const String firstNameFromLogin = "first_name_from_login";
  static const String lastNameFromLogin = "last_name_from_login";
}
