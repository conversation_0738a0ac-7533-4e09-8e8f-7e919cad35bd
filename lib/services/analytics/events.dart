import 'package:device_info_plus/device_info_plus.dart';
import 'package:gt_plus/services/prefs_service.dart';

import '../../models/clinic_details_model.dart';
import 'analytics_service.dart';

enum EventParams {
  ringFirstConnection("RING_FIRST_CONNECTION"),
  loginGT("LOGIN_GT"),
  doneGT("DONE_GT"),
  // resubmitGT("RESUBMIT_GT"),
  nextPatientGT("NEXT_PATIENT_GT");

  final String value;
  const EventParams(this.value);

  Future<void> log(Map<String, Object>? parameters) async {
    final AnalyticsService analyticsService = AnalyticsService();
    final DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
    PrefsService prefsService = PrefsService();
    String deviceName = "Unknown";
    String deviceOS = "Unknown";
    try {
      if (await deviceInfoPlugin.deviceInfo is AndroidDeviceInfo) {
        final androidInfo = await deviceInfoPlugin.androidInfo;
        deviceName = "${androidInfo.manufacturer} ${androidInfo.model}";
        deviceOS = "Android";
      } else if (await deviceInfoPlugin.deviceInfo is IosDeviceInfo) {
        final iosInfo = await deviceInfoPlugin.iosInfo;
        deviceName = iosInfo.name;
        deviceOS = "iOS";
      }
    } catch (e) {
      deviceName = "Unknown";
      deviceOS = "Unknown";
    }

    String identifier = await prefsService.getIdentifier();
    int currentTimestamp = DateTime.now().millisecondsSinceEpoch;
    ClinicDetailsModel? clinicDetails = await prefsService.getClinicDetails();

    parameters = {
      ...(parameters ?? {}),
      "identifier": identifier,
      "device_name": deviceName,
      "device_os": deviceOS,
      "clinicName": clinicDetails?.clinicName ?? "",
      "timestamp": currentTimestamp,
    };

    analyticsService.logEvent(
      eventName: value,
      parameters: parameters,
    );
  }
}

class LogEvents {
  static Future<void> logRingFirstConnectionEvent() async {
    await EventParams.ringFirstConnection.log({});
  }

  static Future<void> logLoginGTEvent({
    required String phoneNumber,
    String? email,
  }) async {
    await EventParams.loginGT.log({
      "phoneNumber": phoneNumber,
      if (email != null) "email": email,
    });
  }

  static Future<void> logDoneGTEvent({
    required String folderName,
  }) async {
    await EventParams.doneGT.log({
      "folderName": folderName,
    });
  }

  // static Future<void> logResubmitGTEvent({
  //   required String folderName,
  // }) async {
  //   await EventParams.resubmitGT.log({
  //     "folderName": folderName,
  //   });
  // }

  static Future<void> logNextPatientGTEvent({
    required String folderName,
  }) async {
    await EventParams.nextPatientGT.log({
      "folderName": folderName,
    });
  }
}
