import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';

class AnalyticsService {
  static AnalyticsService? _instance;
  AnalyticsService._internal();
  factory AnalyticsService() {
    _instance ??= AnalyticsService._internal();
    return _instance!;
  }
  final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;

  Future<void> logEvent({
    required String eventName,
    Map<String, Object>? parameters,
  }) async {
    try {
      _validateEventName(eventName);
      final sanitizedParams = parameters;

      await _analytics.logEvent(
        name: eventName,
        parameters: sanitizedParams,
      );

      _debugLog(eventName, sanitizedParams);
    } on ArgumentError catch (e) {
      _handleError(e);
    } catch (e) {
      _handleError(Exception('Unexpected error logging event: $e'));
    }
  }

  void _validateEventName(String eventName) {
    // Firebase event name restrictions:
    // 1. Must start with letter
    // 2. Can contain letters, numbers, and underscores
    // 3. Maximum 40 characters
    final validEventNamePattern = RegExp(r'^[a-zA-Z][a-zA-Z0-9_]{0,39}$');
    if (!validEventNamePattern.hasMatch(eventName)) {
      throw ArgumentError('Invalid event name: "$eventName". '
          'Must start with a letter, contain only letters, numbers, underscores, '
          'and be 1-40 characters long.');
    }
  }

  void _debugLog(String eventName, Map<String, Object>? parameters) {
    if (kDebugMode) {
      print('✅ Event logged: $eventName');
      if (parameters != null) {
        print('📦 Event Parameters: $parameters');
      }
    }
  }

  void _handleError(Object error) {
    if (kDebugMode) {
      print('❌ Error logging event: $error');
    }
  }
}
