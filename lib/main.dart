import 'dart:ui';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:gt_plus/modules/appUpdate/package_info.dart';
import 'package:gt_plus/modules/ppg/controller/ppg_controller.dart';
import 'package:gt_plus/services/remoteConfig/firebase_remote_config_service.dart';
import 'package:gt_plus/utils/appConst/app_strings.dart';
import 'package:gt_plus/utils/appConst/app_theme.dart';
import 'package:native_device_orientation/native_device_orientation.dart';
import 'package:gt_plus/services/gemini_service.dart';

import 'app_routes.dart';
import 'global_controller.dart';
import 'modules/BleFlutter/ble_flutter_controller.dart';
import 'modules/splash/view/splash_view.dart';
import 'services/sound_service.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  await initializeFirebaseCrashlytics();
  await initializePackageInfo();
  await initializeRemoteConfig();
  await initializeGeminiService();
  await initializeSoundService();
  Get.put(BleFlutterController(), permanent: true);
  Get.put(GlobalController(), permanent: true);
  Get.put(PpgController(), permanent: true);
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.landscapeLeft,
    DeviceOrientation.landscapeRight,
  ]).then((_) {
    runApp(
      NativeDeviceOrientationReader(
        useSensor: true,
        builder: (BuildContext context) {
          return const MainApp();
        },
      ),
    );
  });
}

class MainApp extends StatelessWidget {
  const MainApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: AppStrings.appName,
      getPages: AppRoutes.getPages,
      initialRoute: SplashView.routeName,
      debugShowCheckedModeBanner: false,
      theme: buildAppTheme(),
      builder: (context, child) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(
            textScaler: const TextScaler.linear(1.0),
          ),
          child: child!,
        );
      },
    );
  }
}

Future<void> initializeFirebaseCrashlytics() async {
  FlutterError.onError = (errorDetails) {
    FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
  };
  PlatformDispatcher.instance.onError = (error, stack) {
    FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
    return true;
  };
}

Future<void> initializeRemoteConfig() async {
  final firebaseRemoteConfigService = FirebaseRemoteConfigService();
  await firebaseRemoteConfigService.initialize();
}

Future<void> initializePackageInfo() async {
  final packageInfo = PackageInfoSetup();
  await packageInfo.initialize();
}

Future<void> initializeGeminiService() async {
  final geminiService = Get.put(GeminiService(), permanent: true);
  await geminiService.init();
}

Future<void> initializeSoundService() async {
  await SoundService().initialize();
}
