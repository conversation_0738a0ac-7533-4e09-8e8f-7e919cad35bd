#!/bin/bash

# Define paths
PODS_ROOT="./ios/Pods"
UPLOAD_SYMBOLS_PATH="${PODS_ROOT}/FirebaseCrashlytics/upload-symbols"
GOOGLE_SERVICES_INFO_PLIST="./ios/Runner/GoogleService-Info.plist"
ARCHIVE_PATH="$HOME/Library/Developer/Xcode/Archives"
TEMP_FILE="/tmp/dsym_files.txt"
PROGRESS_FILE="/tmp/dsym_upload_progress.txt"

# Remove old temp files if they exist
rm -f "$TEMP_FILE" "$PROGRESS_FILE"

# Check if upload-symbols script exists
if [ ! -f "$UPLOAD_SYMBOLS_PATH" ]; then
  echo "Error: FirebaseCrashlytics upload-symbols script not found at $UPLOAD_SYMBOLS_PATH"
  echo "Make sure FirebaseCrashlytics pod is installed correctly"
  exit 1
fi

# Check if GoogleService-Info.plist exists
if [ ! -f "$GOOGLE_SERVICES_INFO_PLIST" ]; then
  echo "Error: GoogleService-Info.plist not found at $GOOGLE_SERVICES_INFO_PLIST"
  exit 1
fi

# Find dSYM files in the build directory (from most recent build)
echo "Finding dSYM files in build directory..."
find ./build -name "*.dSYM" -type d > "$TEMP_FILE"

# Find only the most recent archive dSYM files (from the latest build)
echo "Finding dSYM files from the most recent archive..."
# Get the most recent archive directory
LATEST_ARCHIVE=$(find "$ARCHIVE_PATH" -maxdepth 2 -type d -name "*.xcarchive" | sort -r | head -n 1)

if [ -n "$LATEST_ARCHIVE" ]; then
  echo "Latest archive: $LATEST_ARCHIVE"
  find "$LATEST_ARCHIVE" -name "*.dSYM" -type d >> "$TEMP_FILE"
else
  echo "No archives found in $ARCHIVE_PATH"
fi

# Check if any dSYM files were found
if [ ! -s "$TEMP_FILE" ]; then
  echo "No dSYM files found. Make sure you've built the app for a device (not simulator)."
  exit 1
fi

# Count the total number of files to process
TOTAL_FILES=$(wc -l < "$TEMP_FILE")
echo "Found $TOTAL_FILES dSYM files to upload"
echo "0/$TOTAL_FILES" > "$PROGRESS_FILE"

# Display the list of files to be uploaded
echo "Files to be uploaded:"
cat "$TEMP_FILE"
echo ""
echo "Starting upload to Firebase Crashlytics..."

# Process each dSYM file with progress tracking
CURRENT=0
while IFS= read -r DSYM; do
  CURRENT=$((CURRENT + 1))
  echo "[$CURRENT/$TOTAL_FILES] Uploading $DSYM"
  echo "$CURRENT/$TOTAL_FILES" > "$PROGRESS_FILE"
  
  "${UPLOAD_SYMBOLS_PATH}" -gsp "${GOOGLE_SERVICES_INFO_PLIST}" -p ios "${DSYM}" --skip-dsym-validation
  
  if [ $? -eq 0 ]; then
    echo "[$CURRENT/$TOTAL_FILES] Successfully uploaded $DSYM"
  else
    echo "[$CURRENT/$TOTAL_FILES] Failed to upload $DSYM"
  fi
  
  # Display progress percentage
  PERCENT=$(echo "scale=2; ($CURRENT / $TOTAL_FILES) * 100" | bc)
  echo "Progress: $PERCENT% complete"
  echo ""
done < "$TEMP_FILE"

rm -f "$TEMP_FILE" "$PROGRESS_FILE"
echo "dSYM upload process completed." 